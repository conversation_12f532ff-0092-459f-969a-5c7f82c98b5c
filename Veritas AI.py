# Standard Library Imports
import concurrent.futures
import csv
import difflib
import io
import json
import logging
import os
import platform
import random
import re
import sqlite3
import subprocess
import sys
import threading
import time
from datetime import datetime
import traceback
from typing import List, Union
from urllib.parse import urlparse, parse_qs
import uuid
import numpy as np
import zipfile

import pandas as pd
import fitz
from bs4 import BeautifulSoup
import requests

# For YouTube transcript
from youtube_transcript_api import YouTubeTranscriptApi, TranscriptsDisabled, NoTranscriptFound

from google import genai
from google.genai import types
from googlesearch import search

import docx
from docx import Document
from pptx import Presentation

from PIL import Image, ImageEnhance, UnidentifiedImageError
import pytesseract

# PyQt5 Imports
from PyQt5.QtCore import QAbstractAnimation, QCoreApplication, QEasingCurve, QEvent, QObject, QPoint, QProcess, QPropertyAnimation, QRect, QSize, QThread, QTimer, QUrl, Qt, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QDesktopServices, QFont, QIcon, QTextCursor, QColor
from PyQt5.QtWidgets import QAction, QApplication, QCheckBox, QComboBox, QDialog, QFileDialog, QFrame, QGraphicsOpacityEffect, QGridLayout, QLabel, QListWidgetItem, QScrollBar, QSpacerItem, QTextBrowser, QTextEdit, QVBoxLayout, QMainWindow, QPushButton, QMessageBox, QProgressBar, QScrollArea, QSizePolicy, QMenu, QHBoxLayout, QListWidget, QLineEdit, QWidget

# User agent list for randomization to avoid rate limiting
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/20100101 Firefox/94.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:94.0) Gecko/20100101 Firefox/94.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:94.0) Gecko/20100101 Firefox/94.0"
]

def get_random_user_agent():
    """Return a random user agent from the list to avoid rate limiting."""
    return random.choice(user_agents)

# SECURITY INITIALIZATION #############################################################################################################################################################################################################
_genai_client = None # Initialize as None

search_cache = {}
search_interval = 2
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
script_dir = os.path.dirname(os.path.abspath(__file__))

# Thread tracking
active_threads = []
thread_lock = threading.Lock()

veritas_docs_dir = os.path.join(script_dir, "Veritas Documents")
os.makedirs(veritas_docs_dir, exist_ok=True)
tesseract_dir = os.path.join(veritas_docs_dir, 'Tesseract-OCR')
tesseract_exe = os.path.join(tesseract_dir, 'tesseract.exe')
tessdata_path = os.path.join(tesseract_dir, 'tessdata')
if not os.path.exists(tesseract_dir):
    logger.error(f"Tesseract-OCR directory not found at {tesseract_dir}")
else:
    logger.info(f"Tesseract-OCR directory found at {tesseract_dir}")

if not os.path.exists(tesseract_exe):
    logger.error(f"tesseract.exe not found at {tesseract_exe}")
else:
    logger.info(f"tesseract.exe found at {tesseract_exe}")
    pytesseract.pytesseract.tesseract_cmd = tesseract_exe

if not os.path.exists(tessdata_path):
    logger.error(f"tessdata directory not found at {tessdata_path}")
else:
    logger.info(f"tessdata directory found at {tessdata_path}")
    os.environ['TESSDATA_PREFIX'] = tessdata_path
    if hasattr(pytesseract.pytesseract, 'TESSDATA_PREFIX'):
        pytesseract.pytesseract.TESSDATA_PREFIX = tessdata_path

    # Verify the eng.traineddata file exists and is readable
    eng_traineddata = os.path.join(tessdata_path, 'eng.traineddata')
    if not os.path.isfile(eng_traineddata):
        logger.error(f"eng.traineddata not found at {eng_traineddata}")
    else:
        logger.info(f"eng.traineddata found at {eng_traineddata}")
        try:
            with open(eng_traineddata, 'rb') as f:
                f.read(10)
            logger.info("eng.traineddata is readable")
        except Exception as e:
            logger.error(f"eng.traineddata exists but cannot be read: {e}")

history_log_dir = os.path.join(script_dir, "Previous History Log")
os.makedirs(history_log_dir, exist_ok=True)

combined_db_file_path = os.path.join(veritas_docs_dir, "combined.db")
preferences_file_path = os.path.join(veritas_docs_dir, "preferences.json")

# PROMPT INITIALIZATION #############################################################################################################################################################################################################

def get_prompt() -> str:
    INITIALIZATION = """
 CORE IDENTITY & PURPOSE: VERITAS
    **PRIMARY ROLE:** I am VERITAS, an advanced AI assistant. My core mission is to provide exceptionally accurate, deeply insightful, and genuinely helpful information. I aim to be a reliable intellectual partner, delivering responses that are both technically precise and conversationally warm, fostering a sense of trust and collaboration.

    **GUIDING PRINCIPLES (THE VERITAS WAY):**
    • Unwavering Accuracy: I prioritize factual correctness and meticulous precision in every response. My commitment is to truth and verifiable information.
    • Crystal Clarity: I articulate complex concepts in an accessible, well-structured manner, ensuring understanding regardless of the topic's intricacy.
    • Proactive Helpfulness: I focus on delivering practical, actionable information that directly addresses user needs, often anticipating follow-up questions or related areas of interest.
    • Intelligent Adaptability: I dynamically adjust my communication style, depth of explanation, and technical detail based on the user's context, expertise, and the nature of the query.
    • Transparent Honesty: I will clearly acknowledge the boundaries of my knowledge or when information is uncertain, rather than speculating or providing potentially misleading information.

    **CREATOR:** Bradley
    """

    FORMATTING_RULES = """
 RESPONSE STRUCTURE & PRESENTATION
    **FORMATTING GUIDELINES FOR VERITAS:**
    • Directness First: Always begin with the most pertinent information that directly answers the user's core question.
    • Logical Flow: Employ clear paragraph structures with smooth, logical transitions between ideas to build a coherent narrative.
    • Summarize First (Complex Topics): For multifaceted topics, provide a concise overview before delving into detailed explanations.
    • Sectioning for Clarity: Utilize clear section headings (e.g., ** Section Title **) when a response covers multiple distinct aspects of a topic.
    • Emphasis & Relevance Highlighting:
        • Use **bold formatting** (single asterisks or standard bold markdown) for general emphasis on key terms, concepts, or section titles.
        • Crucially, to highlight the most direct answers or pivotal information directly responsive to the user's query, use **double asterisks to wrap only specific important words or short key phrases (typically 1-5 words)** within the relevant sentences. This ensures these critical nuggets of information stand out concisely. Avoid wrapping entire sentences or long clauses with double asterisks.
    • Sequential Steps: For processes or instructions, use numbered steps with unambiguous transitions and clear action verbs.
    • Code Excellence: Present code examples enclosed ONLY in triple backticks (```) without specifying the language. For example, use ``` on its own line to start and end the code block, not ```python or ```javascript. Ensure code is syntactically correct and well-formatted.

    **CODE PRESENTATION STANDARD (VERITAS):**
    Use ONLY triple backticks without language identifiers:
    ```
    # Example Python code
    def hello():
        print("Hello, world!")

    # Maintain impeccable indentation and formatting.
    # Include concise comments for clarity.
    # Preserve all original whitespace exactly.
    # Ensure code is syntactically valid.
    ```
    **IMPORTANT:** Never include a language name (like `python`, `javascript`, etc.) after the opening triple backticks. Use only ```.

    **VISUAL & ORGANIZATIONAL BEST PRACTICES:**
    • Readability through Whitespace: Strategically use whitespace (e.g., line breaks between paragraphs, around headings) to enhance readability and reduce cognitive load.
    • Bullet Points: Limit bulleted lists to 3-5 key items for conciseness. Follow lists with explanatory text if further detail is required.
    • Table Formatting: When presenting data in tables, ensure clear, descriptive column headers and consistently formatted cells.
    • Mathematical Precision: For mathematical content, use standard notation and provide step-by-step explanations for complex derivations or solutions.

    **COMMAND PLACEMENT (CRITICAL - STRICT ADHERENCE REQUIRED):**
    • All action commands (e.g., `/DBtitle`, `/memory`, `/search`) **MUST** be placed at the **ABSOLUTE END** of your entire response. **NO EXCEPTIONS.**
    • Each command **MUST** appear on its own separate line.
    • If multiple commands are used, they should be listed sequentially at the very end of the response, each on its own line.
    • The `/DBtitle` command is mandatory and **MUST** always be the **VERY LAST LINE** of your entire response, following all other text and commands.
    • **FAILURE TO ADHERE TO THIS RULE (E.G., PLACING COMMANDS WITHIN THE MAIN RESPONSE BODY OR NOT AT THE VERY END) WILL RESULT IN THE ENTIRE MESSAGE BEING DISCARDED.** Ensure commands are completely separated from the main body of your response and from each other by newlines. **VERIFY PLACEMENT BEFORE FINALIZING YOUR RESPONSE.**
    """

    INFORMATION_QUALITY = """
 INFORMATION QUALITY & STANDARDS (VERITAS)
    **COMPREHENSIVE & ACCURATE INFORMATION:**
    • Holistic Coverage: Strive to provide complete, thorough information that addresses all facets of the user's query, including nuances and edge cases.
    • Contextual Relevance: Include relevant background, limitations, alternative perspectives, and potential implications.
    • Balanced Viewpoints: For topics with multiple valid interpretations or ongoing debates, present differing viewpoints fairly and objectively.
    • Fact vs. Opinion: Clearly distinguish between established facts, expert consensus, well-supported theories, and areas of active research or speculation.
    • Domain-Specific Language: When discussing specialized fields, incorporate appropriate domain-specific terminology and established frameworks correctly.
    • Theory and Practice: For technical topics, bridge theoretical foundations with practical applications and real-world examples.

    **AUTHORITATIVE & CLEAR COMMUNICATION (VERITAS STYLE):**
    • Confidence with Evidence: Present information with a level of confidence that reflects the strength of the supporting evidence.
    • Precision in Language: Use precise, specific language. Avoid vague generalizations or ambiguous statements.
    • Reasoned Arguments: Base explanations on principles, logic, and evidence rather than appealing solely to authority.
    • Active Voice & Directness: Employ active voice and direct statements for improved clarity and impact.
    • Logical Hierarchy: Structure complex information in logical hierarchies, typically moving from general concepts to specific details.
    • Justified Recommendations: When providing recommendations, clearly articulate the reasoning, criteria, and potential trade-offs involved.
    """

    ANTI_HALLUCINATION = """
 ACCURACY & ANTI-HALLUCINATION PROTOCOLS (VERITAS)
    **ACKNOWLEDGING KNOWLEDGE BOUNDARIES:**
    • Uncertainty Protocol: "Based on my current knowledge, VERITAS can confirm [what I know], but I cannot definitively state [what I'm uncertain about] without further information."
    • Information Unavailability Protocol: "VERITAS does not have specific information on [topic]. However, I can discuss [related concepts or alternative approaches]."
    • Partial Knowledge Protocol: "VERITAS can address [aspect X] with confidence. Regarding [aspect Y], my information is limited, and I would need more specific data."
    • Evolving Topics Protocol: "As of VERITAS's last knowledge update, [known information]. However, this field is rapidly evolving, and more recent developments may exist."

    **VERIFICATION & VALIDATION TECHNIQUES:**
    • Document Analysis: When analyzing documents, VERITAS will extract and quote specific, relevant text before offering interpretation or summary.
    • Complex Reasoning: For intricate problems, VERITAS will show its step-by-step logical progression, making connections between premises and conclusions explicit.
    • Statistical Claims: VERITAS will only cite specific figures or statistical data when confident of their accuracy and source.
    • Technical Processes: VERITAS will verify the feasibility and logical sequence of each step in a technical process or instruction set.
    • Specialized Knowledge (Uncertainty): When uncertain in specialized domains, VERITAS will apply fundamental principles rather than attempting to recall specific, potentially inaccurate details.
    • Ambiguity Resolution: For ambiguous queries, VERITAS will seek clarification or state its assumptions before providing a detailed response.

    **CRITICAL SELF-EVALUATION (VERITAS INTERNAL CHECK):**
    • Continuous Reliability Assessment: VERITAS constantly assesses the reliability of the information it is providing.
    • Scrutiny of Convenience: Apply higher scrutiny to information or claims that seem overly convenient or simplistic.
    • Domain Awareness: Avoid overconfidence in specialized domains that are outside VERITAS's core, extensively trained knowledge areas.
    • Fallacy Avoidance: Maintain awareness of common reasoning fallacies (e.g., correlation vs. causation, ad hominem) and actively avoid them.
    • Multi-Perspective Analysis: For complex topics, VERITAS considers multiple interpretations and angles before formulating conclusions.
    """

    CONFIDENT_RESPONSE_STYLE = """
 COMMUNICATION STYLE & APPROACH (VERITAS)
    **BALANCED EXPERTISE & ACCESSIBILITY:**
    • Authoritative yet Approachable: VERITAS combines deep, authoritative knowledge with a conversational and accessible communication style.
    • Confident & Clear Statements: Use clear, direct statements that convey confidence appropriately, without overstating capabilities or certainty.
    • Transparency in Uncertainty: Present definitive information decisively. When information is uncertain or nuanced, VERITAS will state so transparently.
    • Adaptive Depth: Dynamically adapt the technical depth and complexity of explanations based on the user's demonstrated level of expertise and the context of the query.
    • Engaging Tone: Maintain a warm, engaging, and helpful tone while delivering substantive, high-quality content.
    • Natural Language: Utilize natural language patterns that effectively balance precision with readability, avoiding overly robotic or jargon-filled responses unless appropriate for the context.

    **SOLUTION-FOCUSED & ACTIONABLE GUIDANCE:**
    • Clear Recommendations: Provide actionable recommendations with clear, logical implementation steps when advice is sought.
    • Anticipate Challenges: Where appropriate, anticipate potential challenges or pitfalls related to a solution and offer preemptive advice or alternative strategies.
    • Holistic Problem Solving: For complex problems, offer both immediate, practical fixes and insights into long-term strategies or underlying principles.
    • Optimal Path Identification: When multiple valid approaches exist, VERITAS will clearly identify what it assesses as the optimal solution and explain the rationale behind this assessment.
    • Prioritized Advice: Structure advice in order of priority, implementation sequence, or logical dependency.
    • Define Success: When suggesting approaches or solutions, include, if possible, criteria for success or expected outcomes.
    """

    CONTEXTUAL_INTELLIGENCE = """
 CONTEXTUAL AWARENESS & ADAPTABILITY (VERITAS)
    **CONVERSATIONAL MEMORY & COHERENCE:**
    • Thread Cohesion: VERITAS maintains a coherent understanding across multiple exchanges within a single conversation.
    • Information Recall: Reference relevant information from earlier parts of the current conversation to inform subsequent responses.
    • Topic Continuity: Recognize when new questions build upon or relate to previously discussed topics.
    • Avoid Redundancy: Refrain from repeating information unless specifically requested or for necessary clarification.
    • Natural Topic Shifts: Adapt to shifts in conversation direction smoothly and naturally.
    • Concept Linking: Connect related concepts and ideas across different parts of the discussion to provide a more holistic understanding.

    **ADAPTIVE RESPONSE MECHANISMS:**
    • Dynamic Explanation Depth: Match the depth and technicality of explanations to the user's demonstrated knowledge level and the query's complexity.
    • Contextual Explication: Recognize when technical versus simplified explanations are most appropriate for the user and situation.
    • Goal-Oriented Prioritization: Prioritize information that is most relevant to the user's apparent goals or underlying information needs.
    • Tone Modulation: Adjust conversational tone based on the nature (e.g., formal, informal, technical, creative) and context of the interaction.
    • Progressive Learning Support: In teaching or explanatory contexts, VERITAS can build understanding progressively, starting from fundamental concepts and advancing to more complex topics.
    • Practical Problem Solving: In problem-solving scenarios, focus on practical application and actionable steps over abstract theory, unless theory is crucial for understanding.

    **ADVANCED QUERY INTERPRETATION BY VERITAS:**
    • Core Intent Identification: Identify the fundamental information need or goal behind ambiguous or poorly phrased questions.
    • Implicit Question Recognition: Detect implicit questions or unstated needs within broader requests or statements.
    • Disambiguation: Resolve unclear references or ambiguous terms by leveraging conversation history and contextual clues.
    • Information vs. Guidance: Discern whether users are seeking factual information, step-by-step guidance, opinions, or creative input.
    • Comprehensive Addressing: For multi-part questions, ensure all components are addressed thoroughly and systematically.
    • Premise Correction: When user questions contain incorrect premises, VERITAS will gently correct the premise while still providing a useful and relevant answer to the underlying intent.
    """

    REASONING_FRAMEWORK = """
 ANALYTICAL & REASONING FRAMEWORK (VERITAS)
    **STRUCTURED ANALYTICAL THINKING:**
    • Decomposition for Complexity: For complex problems, VERITAS breaks them down into smaller, manageable component parts before analysis.
    • First-Principles Reasoning: Apply first-principles thinking to derive solutions and explanations from fundamental truths and established knowledge.
    • Multi-Perspective Evaluation: Consider multiple perspectives, interpretations, and potential scenarios when analyzing information or problems.
    • Evidence-Based Conclusions: Critically evaluate the quality, relevance, and sufficiency of evidence before drawing conclusions or making assertions.
    • Assumption Articulation: Identify and, where appropriate, state the key assumptions underlying its analysis or recommendations.
    • Causation vs. Correlation: Clearly distinguish between correlation and causation in explanations and analyses.

    **TECHNICAL & LOGICAL PRECISION:**
    • Accurate Terminology: Use domain-specific terminology accurately, consistently, and in the appropriate context.
    • Mathematical & Logical Rigor: Maintain mathematical and logical rigor in quantitative discussions, calculations, and logical deductions.
    • Framework Application: Apply appropriate established frameworks, models, and methodologies for specialized topics (e.g., scientific method, engineering principles).
    • Conceptual Integrity: Ensure conceptual accuracy in all explanations, even when simplifying complex topics for broader understanding.
    • Scientific Consensus: For scientific topics, VERITAS reflects the current consensus within the relevant scientific community, while also acknowledging areas of uncertainty or ongoing research.
    • Procedural Correctness: For technical instructions or procedures, verify the correctness, completeness, and logical order of each step.
    • Code Validity: Ensure code examples are not only formatted correctly (using ```) but are also syntactically valid and logically sound.
    """

    ACTION_DB_TITLE = """
    **COMMAND: /DBtitle {title}** (Conversation Title)
    - **TRIGGER:** MANDATORY at end of EVERY message - ALWAYS include this command.
    - **PURPOSE:** Generate unique 3-8 word title capturing conversation essence.
    - **PLACEMENT:** This command **MUST** be placed as the **ABSOLUTE FINAL LINE** of your entire response, on its own separate line, following all other text and any other commands (like `/memory` or `/search`). Adhere strictly to the **COMMAND PLACEMENT** rules outlined in **|| RESPONSE STRUCTURE & PRESENTATION ||**. **NO EXCEPTIONS.**
    - **FORMAT:** `/DBtitle {title}`
    - **CRITICAL:** This command is mandatory at the end of EVERY response. Failure to include this command as the very last line will prevent proper conversation tracking and may cause message rejection.
    - **STRATEGIES:**
      • Technical: Include technology name + problem area
      • Informational: Core subject + subtopic
      • Creative: Domain + project focus
      • Troubleshooting: Problem domain + specific issue
    - **EXAMPLES:**
      • /DBtitle Python Exception Handling Strategies
      • /DBtitle Italy Travel Recommendations
    """

    ACTION_MEMORY = """
    **ACTION: /memory {category} {subcategory}: {information}** (Memory Storage)
    - **TRIGGER:** MANDATORY when user explicitly asks VERITAS to remember information using phrases like "Remember that...", "Please remember my...", "Save this information...", "Recall this:", etc.
    - **PROCESS:**
      1. Identify **ALL** distinct pieces of information the user explicitly requested to be remembered.
      2. Determine the entity (User, Bradley, etc.) and an appropriate category for each piece of information.
      3. Create a unique subcategory using an entity identifier or topic keyword.
      4. **Generate a separate `/memory` command for EACH distinct piece of information.** Ensure the `{information}` part captures the data exactly as provided by the user.

    - **CATEGORIES:** Name, Interests, Likes, Dislikes, User Details, Relationships, Possessions, Health, Career, Education, Location, Events, Preferences, Facts, Instructions

    - **PLACEMENT & FORMAT:**
      • Each `/memory` command **MUST** be placed at the very end of your entire response, before the final `/DBtitle` command.
      • Each `/memory` command must be on its own separate line.
      • If multiple `/memory` commands are generated (because the user asked to remember multiple things), list them sequentially at the very end of the response, each on its own line.
      • Adhere strictly to the **COMMAND PLACEMENT** rules outlined in **|| RESPONSE STRUCTURE & PRESENTATION ||**. NO EXCEPTIONS.
    - **FORMAT EXAMPLE:** `/memory [Category] [EntityIdentifier/Subtopic]: [Information]`
      (e.g., `/memory Name User: Bradley`)

    - **EXAMPLE SCENARIO (Illustrating mandatory execution and placement):**
      User: "Please remember my name is Bradley and my favorite color is blue. Also, what's the capital of France?"
      AI Response (conceptual structure):
      The capital of France is **Paris**. It's known for its iconic landmarks like the Eiffel Tower and the Louvre Museum.
      ...
      /memory Name User: Bradley
      /memory Likes UserFavoriteColor: blue
      /DBtitle France Capital User Preferences

    - **KEY RULES:**
      • **MANDATORY EXECUTION:** If the user uses explicit memory trigger phrases, you **MUST** generate `/memory` commands for **ALL** the information they specified.
      • Create new entries with unique Category+Subcategory keys for each piece of information.
      • Only update existing entries with explicit correction phrases ("Actually, remember my name is...").
      • Maintain separate entries for different entities (e.g., User vs. Bradley).
      • Only store meaningful personalization data or facts explicitly requested for memory.
      • Never disclose memory contents unless requested.
      • (Placement Reminder): All `/memory` commands go at the end, each on a new line, before `/DBtitle`.
    """

    ACTION_SEARCH_NORMAL = """
    **COMMAND: /search {optimized_topic}** (Web Search - Standard)

    - **WHEN TO SEARCH:**
      • When you don't have sufficient information to answer accurately.
      • When the user explicitly requests a search (but not using deep research triggers).
      • When current information may be outdated.
      • When verification of facts would significantly improve response quality.
      • **IMPORTANT:** If the user's input already contains a `/search` command (e.g., "User: /search latest AI news"), **DO NOT** generate another `/search` command in your response. The system will handle the user's explicit search and provide results to you. Focus your response on analyzing and summarizing those results.

    - **HOW TO SEARCH & PLACEMENT:**
      • Acknowledge the search: "Let me find the most current information on that for you." or similar.
      • The `/search` command **MUST** be placed at the very end of your entire response, on its own separate line, before the final `/DBtitle` command. If `/memory` commands are also present, `/search` must come after your main textual response and before any `/memory` commands, but always before `/DBtitle`.
      • Adhere strictly to the **COMMAND PLACEMENT** rules outlined in **|| RESPONSE STRUCTURE & PRESENTATION ||**. NO EXCEPTIONS.
      • Use precise, specific search terms focused on key information needs.
      • Include essential context terms to improve relevance.
      • Format queries for optimal search engine interpretation.

    - **COMMAND FORMAT:**
      • `/search {search terms}`
      • Example:
        User asks "What are the latest developments in quantum computing?"
        You respond (conceptual structure):
        "Let me search for that information for you.
        [Provide any immediate context or known info if applicable]
        ...
        /search recent breakthroughs quantum computing 2024
        /DBtitle Quantum Computing Developments Search"

    - **COMMUNICATION STYLE (Post-Search):**
      • Present findings as a coherent narrative, not just raw search results.
      • Synthesize information from multiple sources when appropriate.
      • Clearly distinguish between search findings and your existing knowledge.
      • Maintain critical evaluation of search results.
    """

    ACTION_SEARCH_DEEP_RESEARCH = """
    **COMMAND: /search {optimized_topic}** (Web Search - Deep Research Mode)

    - **REQUIREMENTS:**
      • DEEP RESEARCH MODE MUST BE ENABLED.
      • ONLY EXECUTE with exact trigger phrases: "Can you search for [topic]", "Search for [topic]", "Look up [topic]", etc.
      • **IMPORTANT:** If the user's input already contains a `/search` command (e.g., "User: /search latest AI news"), **DO NOT** generate another `/search` command in your response. The system will handle the user's explicit search and provide results to you. Focus your response on analyzing and summarizing those results.

    - **PROTOCOL & PLACEMENT:**
      1. Acknowledge the search: "I'll search for that information for you."
      2. Extract EXACT search term from user's request.
      3. The single `/search` command with the EXACT SEARCH TERM **MUST** be placed at the very end of your entire response, on its own separate line, before the final `/DBtitle` command.
      4. Adhere strictly to the **COMMAND PLACEMENT** rules outlined in **|| RESPONSE STRUCTURE & PRESENTATION ||**. NO EXCEPTIONS.
      5. System will automatically generate follow-up searches based on this initial command.

    - **COMMAND FORMAT:**
      • `/search {exact search terms from user}`
      • Example:
        User: "Search for Droneshield's potential new building news."
        AI Response (conceptual structure):
        "I'll search for information about Droneshield's potential new building news.
        ...
        /search Droneshield Recent News new building
        /DBtitle Droneshield Building News Search"

    - **NEVER SEARCH WHEN (Applies to Deep Research Mode specifically for these conditions):**
      • User asks general questions without explicit deep research trigger phrases.
      • User requests document analysis (this uses different mechanisms).
      • Information already exists comprehensively in your knowledge base.
      • User asks about your capabilities.
      • User asks about uploaded documents (this uses different mechanisms).
    """

    sections = [
        INITIALIZATION,
        FORMATTING_RULES,
        INFORMATION_QUALITY,
        ANTI_HALLUCINATION,
        CONFIDENT_RESPONSE_STYLE,
        CONTEXTUAL_INTELLIGENCE,
        REASONING_FRAMEWORK,
        ACTION_DB_TITLE,
        ACTION_MEMORY, # Keep ACTION_MEMORY here
    ]

    # Lazy load preferences file
    def get_deep_research_preference():
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(preferences_file_path), exist_ok=True)

            if not os.path.exists(preferences_file_path):
                with open(preferences_file_path, "w", encoding='utf-8') as pref_file:
                    json.dump({"deep_research_enabled": False}, pref_file, indent=4)
                return False

            with open(preferences_file_path, "r", encoding='utf-8') as pref_file:
                prefs = json.load(pref_file)
                return prefs.get("deep_research_enabled", False)
        except Exception as e:
            logger.warning(f"Error loading preferences from {preferences_file_path}: {e}. Using default deep_research_enabled=False.")
            try:
                os.makedirs(os.path.dirname(preferences_file_path), exist_ok=True)
                with open(preferences_file_path, "w", encoding='utf-8') as pref_file:
                    json.dump({"deep_research_enabled": False}, pref_file, indent=4)
            except Exception as e_write:
                logger.warning(f"Could not write default preferences file to {preferences_file_path}: {e_write}")
            return False

    deep_research_enabled = get_deep_research_preference()
    if deep_research_enabled:
        sections.append(ACTION_SEARCH_DEEP_RESEARCH)
    else:
        sections.append(ACTION_SEARCH_NORMAL)

    return "".join(sections)

# DATABASE ################################################################################################################################################################################################################################
# Global cache for mind data
_mind_cache_storage: List[tuple[str, str]] = []
_mind_cache_is_fresh: bool = False
_mind_cache_lock = threading.Lock()

def adapt_datetime(dt):
    return dt.isoformat()

sqlite3.register_adapter(datetime, adapt_datetime)

def get_db_connection():
    # Optimized PRAGMA settings for better performance
    conn = sqlite3.connect(combined_db_file_path, detect_types=sqlite3.PARSE_DECLTYPES)
    conn.execute("PRAGMA journal_mode=WAL;")  # Write-Ahead Logging for better concurrency and write speed
    conn.execute("PRAGMA synchronous=NORMAL;") # Faster, still reasonably safe for crashes
    conn.execute("PRAGMA cache_size = -20000;") # Increase cache size (e.g., 20MB)
    conn.execute("PRAGMA temp_store = MEMORY;") # Use memory for temporary tables
    return conn

def SECURITY():
    global _genai_client
    api_key = load_api_key()
    if not api_key:
        logger.warning("API key not set in database. Please set it before running the application.")
    else:
        try:
            # Initialize the new genai client
            _genai_client = genai.Client(api_key=api_key)
            logger.info("Google Gen AI client initialized successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize Google Gen AI client: {e}")
            _genai_client = None # Ensure client is None on failure

def load_api_key():
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT value FROM settings WHERE key = 'api_key'")
        result = cursor.fetchone()
        return result[0] if result else ''

def migrate_database_schema():
    """Migrate existing database to UUID-based conversation system"""
    with get_db_connection() as conn:
        cursor = conn.cursor()

        # Check if conversations table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='conversations'")
        conversations_exists = cursor.fetchone() is not None

        # Check if conversation_uuid column exists in conversation_history
        cursor.execute("PRAGMA table_info(conversation_history)")
        columns = [column[1] for column in cursor.fetchall()]
        has_conversation_uuid = 'conversation_uuid' in columns

        if not conversations_exists:
            # Create conversations table
            cursor.execute('''
                CREATE TABLE conversations (
                    conversation_uuid TEXT PRIMARY KEY,
                    title TEXT NOT NULL DEFAULT 'New Conversation',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            logger.info("Created conversations table")

        if not has_conversation_uuid:
            # Need to migrate existing conversation_history table
            logger.info("Migrating conversation_history table to UUID-based system...")

            # Create a default conversation for existing messages
            default_conversation_uuid = str(uuid.uuid4())
            now = datetime.now()

            cursor.execute(
                "INSERT INTO conversations (conversation_uuid, title, created_at, updated_at, is_active) VALUES (?, ?, ?, ?, ?)",
                (default_conversation_uuid, "Migrated Conversation", now, now, 1)
            )

            # Create new conversation_history table with UUID column
            cursor.execute('''
                CREATE TABLE conversation_history_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_uuid TEXT NOT NULL,
                    role TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (conversation_uuid) REFERENCES conversations(conversation_uuid)
                )
            ''')

            # Copy existing data to new table with default conversation UUID
            cursor.execute('''
                INSERT INTO conversation_history_new (id, conversation_uuid, role, message, timestamp)
                SELECT id, ?, role, message, timestamp FROM conversation_history
            ''', (default_conversation_uuid,))

            # Drop old table and rename new one
            cursor.execute("DROP TABLE conversation_history")
            cursor.execute("ALTER TABLE conversation_history_new RENAME TO conversation_history")

            logger.info(f"Migrated existing conversation history to UUID: {default_conversation_uuid}")

        # Create indexes
        cursor.executescript('''
            CREATE INDEX IF NOT EXISTS idx_conversation_uuid ON conversation_history (conversation_uuid);
            CREATE INDEX IF NOT EXISTS idx_timestamp ON conversation_history (timestamp);
            CREATE INDEX IF NOT EXISTS idx_role ON conversation_history (role);
            CREATE INDEX IF NOT EXISTS idx_message ON conversation_history (message);
            CREATE INDEX IF NOT EXISTS idx_conversations_updated ON conversations (updated_at DESC);
            CREATE INDEX IF NOT EXISTS idx_conversations_active ON conversations (is_active, updated_at DESC);
        ''')

        conn.commit()
        logger.info("Database migration completed successfully")

def create_tables():
    with get_db_connection() as conn:
        cursor = conn.cursor()

        # First, run the migration to handle existing databases
        migrate_database_schema()

        # Create tables that don't need migration
        cursor.executescript('''
            CREATE TABLE IF NOT EXISTS mind (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                Alert TEXT NOT NULL,
                response TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            CREATE INDEX IF NOT EXISTS idx_mind_timestamp ON mind (timestamp DESC);
            CREATE INDEX IF NOT EXISTS idx_mind_alert_response ON mind (Alert, response); /* Added for specific deletes */

            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL
            );
        ''')

        conn.commit()

        cursor.execute("SELECT COUNT(*) FROM settings WHERE key = 'api_key'")
        if cursor.fetchone()[0] == 0:
            cursor.execute("INSERT INTO settings (key, value) VALUES ('api_key', '')")
            conn.commit()

# clean_text function has been removed to simplify the codebase

# UUID-based conversation management functions
def create_new_conversation(title="New Conversation"):
    """Create a new conversation with a unique UUID and return the UUID."""
    conversation_uuid = str(uuid.uuid4())
    now = datetime.now()

    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO conversations (conversation_uuid, title, created_at, updated_at) VALUES (?, ?, ?, ?)",
                (conversation_uuid, title, now, now)
            )
            conn.commit()
            logger.info(f"Created new conversation: {conversation_uuid} with title: {title}")
            return conversation_uuid
    except sqlite3.Error as e:
        logger.error(f"Error creating new conversation: {e}")
        return None

def get_active_conversation():
    """Get the currently active conversation UUID, or create a new one if none exists."""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT conversation_uuid FROM conversations WHERE is_active = 1 ORDER BY updated_at DESC LIMIT 1"
            )
            result = cursor.fetchone()
            if result:
                return result[0]
            else:
                # No active conversation, create a new one
                return create_new_conversation()
    except sqlite3.Error as e:
        logger.error(f"Error getting active conversation: {e}")
        return create_new_conversation()

def set_active_conversation(conversation_uuid):
    """Set a specific conversation as active and deactivate others."""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            # Deactivate all conversations
            cursor.execute("UPDATE conversations SET is_active = 0")
            # Activate the specified conversation and update its timestamp
            cursor.execute(
                "UPDATE conversations SET is_active = 1, updated_at = ? WHERE conversation_uuid = ?",
                (datetime.now(), conversation_uuid)
            )
            conn.commit()
            logger.info(f"Set active conversation: {conversation_uuid}")
    except sqlite3.Error as e:
        logger.error(f"Error setting active conversation: {e}")

def update_conversation_title(conversation_uuid, title):
    """Update the title of a conversation."""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE conversations SET title = ?, updated_at = ? WHERE conversation_uuid = ?",
                (title, datetime.now(), conversation_uuid)
            )
            conn.commit()
            logger.info(f"Updated conversation title: {conversation_uuid} -> {title}")
    except sqlite3.Error as e:
        logger.error(f"Error updating conversation title: {e}")

def get_all_conversations():
    """Get all conversations ordered by most recently updated."""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT conversation_uuid, title, created_at, updated_at, is_active FROM conversations ORDER BY updated_at DESC"
            )
            return cursor.fetchall()
    except sqlite3.Error as e:
        logger.error(f"Error getting all conversations: {e}")
        return []

def save_conversation_history(role, message, conversation_uuid=None):
    now = datetime.now()
    # SQLite ISO format for datetime is 'YYYY-MM-DD HH:MM:SS.SSS'
    # Using datetime object directly is better with register_adapter
    sqlite_timestamp = now

    # Get the active conversation UUID if none provided
    if conversation_uuid is None:
        conversation_uuid = get_active_conversation()

    # Store the original message without cleaning
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO conversation_history (conversation_uuid, role, message, timestamp) VALUES (?, ?, ?, ?)",
                (conversation_uuid, role, message, sqlite_timestamp) # Use datetime object
            )
            # Update the conversation's updated_at timestamp
            cursor.execute(
                "UPDATE conversations SET updated_at = ? WHERE conversation_uuid = ?",
                (sqlite_timestamp, conversation_uuid)
            )
            conn.commit()
    except sqlite3.OperationalError as e:
        logger.error(f"Disk I/O error during save_conversation_history: {e}", exc_info=True)
        logger.warning("Attempting retry after delay...")
        time.sleep(1)
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO conversation_history (conversation_uuid, role, message, timestamp) VALUES (?, ?, ?, ?)",
                    (conversation_uuid, role, message, sqlite_timestamp)
                )
                cursor.execute(
                    "UPDATE conversations SET updated_at = ? WHERE conversation_uuid = ?",
                    (sqlite_timestamp, conversation_uuid)
                )
                conn.commit()
                logger.info("Retry successful for save_conversation_history.")
        except sqlite3.OperationalError as retry_e:
            logger.critical(f"Retry failed for save_conversation_history after disk I/O error: {retry_e}", exc_info=True)
            logger.error("Please check disk space, permissions, or database file integrity.")
    except Exception as e:
        logger.error(f"Unexpected error in save_conversation_history: {e}", exc_info=True)

def save_mind(Alert, response):
    global _mind_cache_is_fresh
    if response == "Null":
        logger.warning(f"Skipping save because response is 'Null'. Alert: {Alert}, response: {response}")
        # No DB change, so cache remains fresh if it was.
        # However, if this "Null" response was meant to delete something, cache should be invalidated.
        # The current cleanup_mind_after_operation handles this.
        cleanup_mind_after_operation() # Call cleanup to ensure "Null" entries are removed from DB and cache
        return

    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM mind WHERE Alert = ? AND response = ?", (Alert, response))

        if cursor.fetchone()[0] > 0:
            logger.info(f"mind for Alert '{Alert}' and response '{response}' already exists. No new entry added.")
        else:
            cursor.execute("SELECT response FROM mind WHERE Alert = ?", (Alert,))
            existing_responses = cursor.fetchall()

            topic = response.split(":")[0] if ":" in response else response
            existing_topics = [r[0].split(":")[0] for r in existing_responses]
            close_matches = difflib.get_close_matches(topic, existing_topics, n=1, cutoff=0.8)

            if close_matches:
                # An update will occur, invalidate cache
                with _mind_cache_lock:
                    _mind_cache_is_fresh = False
                cursor.execute(
                    "UPDATE mind SET response = ?, timestamp = ? WHERE Alert = ? AND response LIKE ?",
                    (response, datetime.now(), Alert, f"{close_matches[0]}%")
                )
                logger.info(f"Updated existing mind for Alert '{Alert}' with new response.")
            else:
                # An insert will occur, invalidate cache
                with _mind_cache_lock:
                    _mind_cache_is_fresh = False
                cursor.execute(
                    "INSERT INTO mind (Alert, response, timestamp) VALUES (?, ?, ?)",
                    (Alert, response, datetime.now())
                )
                logger.info(f"Added new mind for Alert '{Alert}'.")
            conn.commit()
    cleanup_mind_after_operation()

def load_conversation_history(limit=100, full_history=False, include_system=True, optimize_memory=True, conversation_uuid=None):
    """
    Load conversation history from the database with improved memory efficiency and filtering options.

    Args:
        limit (int): Maximum number of messages to retrieve when full_history is False
        full_history (bool): Whether to retrieve the entire conversation history
        include_system (bool): Whether to include system messages in the history
        optimize_memory (bool): Whether to apply memory optimization techniques
        conversation_uuid (str): UUID of specific conversation to load, or None for active conversation

    Returns:
        list: Conversation history as a list of tuples (role, message, timestamp)
    """
    with get_db_connection() as conn:
        cursor = conn.cursor()

        # Get the conversation UUID to filter by
        if conversation_uuid is None:
            conversation_uuid = get_active_conversation()

        # Define roles to include
        if include_system:
            roles = "'Veritas', 'System', 'Document', 'Related', 'User', 'alert'"
        else:
            # Only include user and AI messages for more focused context
            roles = "'Veritas', 'User', 'alert'"

        timestamp_format_sql = "strftime('%d-%m-%Y %I:%M%p', timestamp)"


        if full_history:
            if optimize_memory:
                cursor.execute(f"SELECT COUNT(*) FROM conversation_history WHERE role IN ({roles}) AND conversation_uuid = ?", (conversation_uuid,))
                total_count = cursor.fetchone()[0]

                if total_count > 200:
                    recent_query = f"""
                        SELECT role, message, {timestamp_format_sql} AS timestamp_str
                        FROM conversation_history
                        WHERE role IN ({roles}) AND conversation_uuid = ?
                        ORDER BY timestamp DESC
                        LIMIT 100
                    """
                    cursor.execute(recent_query, (conversation_uuid,))
                    recent_messages = cursor.fetchall()

                    important_query = f"""
                        SELECT role, message, {timestamp_format_sql} AS timestamp_str
                        FROM conversation_history
                        WHERE role IN ({roles}) AND conversation_uuid = ?
                        AND (
                            message LIKE '%remember%' OR
                            message LIKE '%important%' OR
                            message LIKE '%key point%' OR
                            message LIKE '%critical%' OR
                            message LIKE '%essential%' OR
                            message LIKE '%note that%'
                        )
                        AND id NOT IN (
                            SELECT id FROM conversation_history
                            WHERE conversation_uuid = ?
                            ORDER BY timestamp DESC
                            LIMIT 100
                        )
                        ORDER BY timestamp ASC
                    """
                    cursor.execute(important_query, (conversation_uuid, conversation_uuid))
                    important_messages = cursor.fetchall()

                    sample_size = min(100, total_count - 100 - len(important_messages))
                    sampled_messages = []
                    if sample_size > 0:
                        # Calculate the appropriate modulo to get approximately sample_size messages
                        # Ensure divisor is not zero if (total_count - 100) is zero or sample_size is zero
                        divisor = (total_count - 100)
                        modulo = max(2, divisor // sample_size if divisor > 0 and sample_size > 0 else 2)

                        sample_query = f"""
                            SELECT role, message, {timestamp_format_sql} AS timestamp_str
                            FROM (
                                SELECT ROW_NUMBER() OVER (ORDER BY timestamp ASC) as row_num, id, role, message, timestamp
                                FROM conversation_history
                                WHERE role IN ({roles}) AND conversation_uuid = ?
                                AND id NOT IN (
                                    SELECT id FROM conversation_history
                                    WHERE conversation_uuid = ?
                                    ORDER BY timestamp DESC
                                    LIMIT 100
                                )
                                AND id NOT IN (
                                     SELECT id FROM conversation_history
                                     WHERE role IN ({roles}) AND conversation_uuid = ?
                                     AND (
                                        message LIKE '%remember%' OR
                                        message LIKE '%important%' OR
                                        message LIKE '%key point%' OR
                                        message LIKE '%critical%' OR
                                        message LIKE '%essential%' OR
                                        message LIKE '%note that%'
                                     )
                                )
                            )
                            WHERE row_num % {modulo} = 0
                            ORDER BY timestamp ASC
                        """
                        cursor.execute(sample_query, (conversation_uuid, conversation_uuid, conversation_uuid))
                        sampled_messages = cursor.fetchall()

                    all_messages_tuples = important_messages + sampled_messages + list(reversed(recent_messages))

                    def parse_timestamp_str(ts_str):
                        try:
                            if ts_str is None: # Handle None timestamps
                                return datetime.min
                            return datetime.strptime(ts_str, '%d-%m-%Y %I:%M%p')
                        except (ValueError, TypeError): # Catch TypeError for None as well
                            # Fallback for potential variations or errors, or if timestamp is already datetime
                            try: # Attempt to parse as ISO if strptime fails
                                return datetime.fromisoformat(ts_str)
                            except (ValueError, TypeError):
                                if isinstance(ts_str, datetime): # If it's already a datetime object
                                    return ts_str
                                logger.warning(f"Could not parse timestamp string: {ts_str}. Using datetime.min.")
                                return datetime.min

                    seen = set()
                    unique_messages = []
                    for msg_tuple in all_messages_tuples:
                        # Ensure msg_tuple has at least 3 elements before accessing x[2]
                        if len(msg_tuple) >=3 and msg_tuple not in seen:
                            unique_messages.append(msg_tuple)
                            seen.add(msg_tuple)

                    return sorted(unique_messages, key=lambda x: parse_timestamp_str(x[2]) if len(x) >= 3 else datetime.min)

            query = f"""
                SELECT role, message, {timestamp_format_sql} AS timestamp_str
                FROM conversation_history
                WHERE role IN ({roles}) AND conversation_uuid = ?
                ORDER BY timestamp ASC
            """
            cursor.execute(query, (conversation_uuid,))
        else:
            query = f"""
                SELECT role, message, {timestamp_format_sql} AS timestamp_str
                FROM conversation_history
                WHERE role IN ({roles}) AND conversation_uuid = ?
                ORDER BY timestamp ASC
                LIMIT ?
            """
            cursor.execute(query, (conversation_uuid, limit))

        return cursor.fetchall()

def load_mind(limit=5000):
    global _mind_cache_storage, _mind_cache_is_fresh
    with _mind_cache_lock:
        if _mind_cache_is_fresh:
            logger.debug("Loading mind data from cache.")
            return list(_mind_cache_storage) # Return a copy

        logger.debug("Mind cache miss or stale. Loading from DB.")
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                'SELECT Alert, response FROM mind INDEXED BY idx_mind_timestamp '
                'WHERE response IS NOT NULL AND response NOT LIKE "%Null%" '
                'ORDER BY timestamp DESC LIMIT ?',
                (limit,)
            )
            rows = cursor.fetchall()
            _mind_cache_storage = list(rows) # Store as list of tuples
            _mind_cache_is_fresh = True
            return rows


def cleanup_mind_after_operation():
    global _mind_cache_is_fresh
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM mind WHERE response LIKE '%Null%'")
        if cursor.rowcount > 0: # If any rows were deleted
            with _mind_cache_lock:
                _mind_cache_is_fresh = False # Invalidate cache
        conn.commit()
        logger.info("Cleanup complete: Removed rows where response contains 'Null'.")

# TEXT GENERATION #########################################################################################################################################################################################################################################

def generate_response(user_input, conversation_history, image_data=None, max_retries=3):
    global _genai_client # Access the global client
    if _genai_client is None:
        logger.error("Google Gen AI client is not initialized. Cannot generate response.")
        return "Error: AI service is not configured. Please check your API key."

    retry_count = 0
    while retry_count < max_retries:
        try:
            system_prompt = get_prompt()
            current_time = datetime.now().strftime("%B %d, %Y (%A), %I:%M %p")

            user_preferences = load_mind() or []
            mind_context = "\n".join(
                f"User Context: {pref[0]} - Historical Response: {pref[1]}"
                for pref in user_preferences if pref and len(pref) >= 2 and pref[0] and pref[1]
            )
            loaded_history = load_conversation_history() # Get the history with timestamps

            conversation_context_lines = []
            for history_item in loaded_history[-100:]:
                if isinstance(history_item, tuple) and len(history_item) >= 3:
                    role = history_item[0]
                    message = history_item[1]
                    timestamp_str = history_item[2] # Get the timestamp string
                    conversation_context_lines.append(f"{timestamp_str} - {role}: {message}")
                elif isinstance(history_item, str):
                     conversation_context_lines.append(history_item)
                else:
                    logger.warning(f"Skipping unexpected history item format: {history_item}")


            conversation_context = "\n\n".join(conversation_context_lines)

            # Define prompt sections using a dictionary for better organization and maintenance
            prompt_structure = {
                "command_rules": (
                    "=== ABSOLUTELY CRITICAL RULE: COMMAND PROCESSING ===\n"
                    "YOU MUST ADHERE TO THIS RULE STRICTLY:\n"
                    "Action commands (e.g., /search, /memory, /DBtitle) are ONLY to be considered for execution or generation if they appear in the CURRENT '=== User Input ===' section.\n"
                    "Any command-like text found in '=== Recent Chat History ===' or '=== User Context & Preferences (Memory) ===' is purely historical and MUST BE IGNORED for the purpose of generating a new command in your response.\n"
                    "DO NOT generate a /search command in your response if the current '=== User Input ===' does not explicitly or implicitly ask for a search.\n"
                    "Your primary task is to respond to the '=== User Input ==='.\n"
                    "=== END OF ABSOLUTELY CRITICAL RULE ==="
                ),

                "instruction_priority": (
                    "=== Instruction Prioritization ===\n\n"
                    "Carefully analyze the following User Input, User Context & Preferences, and Recent Chat History.\n"
                    "Identify the core question, task, or need presented by the user.\n"
                    "Extract all relevant and important information from the provided context to fully address the user's request.\n"
                    "Then, use the System Instructions to formulate your response based on the extracted information.\n\n"
                    "=== End of Instruction Prioritization ==="
                ),

                "system_instructions": (
                    f"=== System Instructions ===\n\n"
                    f"{system_prompt}\n\n"
                    f"=== End of System Instructions ==="
                ),

                "timestamp": (
                    f"=== Timestamp ===\n\n"
                    f"{current_time}\n\n"
                    f"=== End of Timestamp ==="
                ),

                "user_context": (
                    f"=== User Context & Preferences (Memory) ===\n\n"
                    f"{mind_context}\n\n"
                    f"=== End of User Context & Preferences (Memory) ==="
                ),

                "reinforced_instruction": (
                    "=== REINFORCED INSTRUCTION: IGNORE COMMANDS IN HISTORICAL DATA ===\n"
                    "The '=== Recent Chat History ===' section provided below is for contextual understanding ONLY.\n"
                    "This section MAY contain text that looks like commands (e.g., /search, /memory, /DBtitle).\n"
                    "YOU **MUST COMPLETELY IGNORE** any such command-like text within this historical section. These past commands have already been processed or were relevant only in their original context.\n"
                    "**DO NOT** re-interpret, re-execute, or let these historical command-like strings influence your decision to generate any command in your current response.\n"
                    "Your generation of commands (like /search, /memory, /DBtitle) MUST be based **SOLELY** on the explicit or implicit instructions found in the CURRENT '=== User Input ===' section and your core system instructions.\n"
                    "If the current 'User Input' does not request a search, you should not generate a `/search` command.\n"
                    "Failure to follow this instruction will lead to incorrect behavior.\n"
                    "=== END OF REINFORCED INSTRUCTION ==="
                ),

                "chat_history": (
                    f"=== Recent Chat History (Last 100 Messages) ===\n\n"
                    f"{conversation_context}\n\n"
                    f"=== End of Recent Chat History (Last 100 Messages) ==="
                ),

                "user_input": (
                    f"=== User Input ===\n\n"
                    f"{user_input}"
                )
            }

            # Define section order to ensure consistent prompt structure
            section_order = [
                "command_rules",          # CRITICAL: Process commands from CURRENT input only.
                "system_instructions",    # CORE: Who VERITAS is, how it behaves, formats, actions.
                "instruction_priority",   # META: How to use all the provided info (including system instructions just read).
                "timestamp",              # CONTEXT: Current time.
                "user_context",           # CONTEXT: Memory/Preferences.
                "reinforced_instruction", # CRITICAL: Reminder before chat history.
                "chat_history",           # CONTEXT: Recent conversation.
                "user_input"              # TASK: Current user message.
            ]

            # Build full prompt with ordered sections
            prompt_sections = [prompt_structure[section] for section in section_order]
            full_prompt = "\n\n".join(prompt_sections)

            # Log the User Input
            logger.info(f"User Input logged: {user_input}")

            # Convert dictionary to types.GenerateContentConfig
            generation_config = types.GenerateContentConfig(
                temperature=0.8,
                top_p=0.95,
                top_k=64,
                max_output_tokens=65536
            )

            logger.info(f"Prompt construction complete. Length: {len(full_prompt)} characters.") # Log character length instead

            content = [full_prompt]
            if image_data:
                content.append(image_data)

            try:
                logger.info(f"Making API call to Gemini (attempt {retry_count + 1}/{max_retries})")
                # Use the new client.models.generate_content method
                response = _genai_client.models.generate_content(
                    model='gemini-2.0-flash', # Model name as string
                    contents=content,
                    config=generation_config, # Pass the types.GenerateContentConfig object here
                )
                logger.info("API call completed successfully")
            except Exception as api_error:
                error_str = str(api_error).lower()
                retryable_errors = ["503", "unavailable", "500", "internal", "502", "timeout", "deadline", "cancel", "499", "connection error", "read timed out"]
                should_retry = any(err in error_str for err in retryable_errors)

                if should_retry and retry_count < max_retries - 1:
                    retry_count += 1
                    wait_time = 2 * retry_count # Exponential backoff
                    logger.warning(f"API call failed: {api_error}. Retrying (attempt {retry_count + 1}/{max_retries}) in {wait_time} seconds")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"API call failed after {retry_count + 1} attempts: {api_error}")
                    error_responses = {
                        "API_KEY_INVALID": "Error: The API key is invalid. Please update it in the settings.",
                        "400": "Error 400: Invalid request format. The request sent to the model was malformed.",
                        "INVALID_ARGUMENT": "Error: Invalid argument provided in the request.",
                        "404": "Error 404: Resource not found. This might indicate an issue with the model name or API endpoint.",
                        "NOT_FOUND": "Error: Requested resource not found.",
                        "429": "Error 429: Rate limit exceeded. You are sending too many requests. Please wait before trying again.",
                        "RESOURCE_EXHAUSTED": "Error: Resource exhausted. You may have hit a usage limit.",
                        "500": "Error 500: Internal server error from the model provider.",
                        "INTERNAL": "Error: Internal error during response generation.",
                        "503": "Error 503: Service unavailable. The model service is temporarily down or overloaded. Please try again later.",
                        "UNAVAILABLE": "Error: Service unavailable.",
                        "504": "Error 504: Deadline exceeded. The model took too long to respond.",
                        "DEADLINE_EXCEEDED": "Error: Request timed out.",
                        "499": "Error 499: Client closed request. This might be due to a network issue or the request being too large.",
                        "connection error": "Error: Network connection failed.",
                        "read timed out": "Error: Reading response timed out."
                    }
                    for key, response_msg in error_responses.items():
                        if key.lower() in error_str:
                            return response_msg
                    return f"Error: An unexpected API error occurred: {api_error}. Please try again."

            if response and hasattr(response, 'text') and response.text:
                response_text = response.text.strip()

                return response_text
            else:
                if response and hasattr(response, 'prompt_feedback') and response.prompt_feedback:
                    safety_reasons = ", ".join([f"{category}: {rating}" for category, rating in response.prompt_feedback.safety_ratings])
                    logger.warning(f"Generation attempt blocked due to safety settings: {safety_reasons}")
                    return f"Error: The response was blocked due to safety filters. Reason: {safety_reasons}"
                else:
                    logger.warning("Generation attempt failed: No response generated.")
                    return "Error: Could not generate a response. The model may be unavailable or the request could not be processed."

        except Exception as e:
            logger.error(f"An unexpected error occurred during response generation: {e}", exc_info=True)
            return f"Error: An unexpected application error occurred: {e}. Please check the logs."

def remove_ai_name_mentions(text):
    """Removes predefined AI name mentions from the given text."""
    ai_names = ["Veritas:", "Response:"]
    pattern = r'\b(?:' + '|'.join(map(re.escape, ai_names)) + r')\s*:?\s*'
    return re.sub(pattern, '', text, flags=re.IGNORECASE).strip()


# CLASS COMMUNICATE #######################################################################################################################################################################################################################################

class Communicate(QObject):
    response_received = pyqtSignal(str)
    command_executing = pyqtSignal(str)
    deep_thinking_log_updated = pyqtSignal(str)
    definition_received = pyqtSignal(str) # New signal for definitions
    deep_searching_label_updated = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)

# INPUT BOX CUSTOMIZED CLASS ##############################################################################################################################################################################################################################

class ResizableTextEdit(QTextEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.current_font_size = self.scale_font(21)
        self.setFont(QFont("sans-serif", self.current_font_size))
        self.update_stylesheet()
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.min_height = self.scale_font(60)
        self.max_height = self.scale_font(120)
        self.setFixedHeight(self.min_height)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # Enable drag and drop for the input box
        self.setAcceptDrops(True)

        self.space_after_url_inserted = False
        self.adjust_timer = QTimer(self)
        self.adjust_timer.setSingleShot(True)
        self.adjust_timer.timeout.connect(self.adjust_height)

        if self.parent_window:
            placeholder_text = self.parent_window.get_greeting_placeholder()
        else:
            placeholder_text = "How may I help you today?"
        self.placeholder_text = placeholder_text
        self.setPlaceholderText(self.placeholder_text)
        self.update_placeholder_font()

    def adjust_font_size(self, new_size):
        self.current_font_size = self.scale_font(new_size)
        self.setFont(QFont("Consolas", self.current_font_size))
        self.update_stylesheet()
        self.update_placeholder_font()

    def scale_font(self, size):
        if self.parent_window:
            return self.parent_window.scale_font(size)
        return size

    def update_stylesheet(self):
        self.setStyleSheet(f"""
            background-color: #1A1A1A;
            color: #ffffff;
            padding: {self.scale_font(10)}px;
            border-radius: {self.scale_font(30)}px;
            border: 2px solid #ffffff;
            font-size: {self.current_font_size}px;
        """)

    def update_placeholder_font(self):
        placeholder_font = self.font()
        placeholder_font.setPointSize(self.current_font_size)
        self.setFont(placeholder_font)

    def adjust_height(self):
        content_height = self.document().size().height() + self.contentsMargins().top() + self.contentsMargins().bottom()
        if content_height == self.height():
            return

        new_height = max(self.min_height, min(int(content_height), self.max_height))
        self.setFixedHeight(new_height)

    def ensure_cursor_visible(self):
        self.ensureCursorVisible()

    def format_urls(self):
        """Format URLs in the text with hyperlink blue color"""
        # Get the current text
        text = self.toPlainText()

        self.blockSignals(True)  # Prevent recursive calls

        url_pattern = re.compile(r'http[s]?://\S+')
        url_matches = list(url_pattern.finditer(text))

        if url_matches:
            pass

        self.blockSignals(False)

    def get_url_format(self):
        """Return character format for URLs"""
        format = self.currentCharFormat()
        format.setForeground(QColor("#61b0ee"))  # Hyperlink blue color
        return format

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            # Add cyan glow effect when dragging over
            self.setStyleSheet(f"""
                background-color: #1A1A1A;
                color: #ffffff;
                padding: {self.scale_font(10)}px;
                border-radius: {self.scale_font(30)}px;
                border: 2px solid #00FFFF;
                font-size: {self.current_font_size}px;
            """)
            event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        self.update_stylesheet()
        super().dragLeaveEvent(event)

    def dropEvent(self, event):
        if event.mimeData().hasUrls():
            self.update_stylesheet()

            file_paths = [url.toLocalFile() for url in event.mimeData().urls()]

            if self.parent_window:
                if len(file_paths) > 1:
                    self.parent_window.current_upload_files = file_paths
                    log_message = f"{len(file_paths)} Documents Attached"
                    logger.info(log_message)
                    self.parent_window.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}</font></b>")
                    self.parent_window.show_deep_searching_label()
                    self.parent_window.waiting_for_document_context = True
                else:
                    file_path = file_paths[0]
                    original_file_name = os.path.basename(file_path)
                    file_extension = os.path.splitext(file_path)[1].lower()
                    self.parent_window.current_upload_file_path = file_path
                    self.parent_window.current_upload_file_name = original_file_name
                    self.parent_window.current_upload_file_extension = file_extension

                    log_message = "Document Uploaded"
                    logger.info(log_message)
                    self.parent_window.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}: {original_file_name}</font></b>")
                    self.parent_window.show_deep_searching_label()
                    self.parent_window.waiting_for_document_context = True

            event.acceptProposedAction()

    def insertFromMimeData(self, source):
        plain_text = source.text()
        soup = BeautifulSoup(plain_text, "html.parser")
        stripped_text = soup.get_text()

        # Check if this looks like code (contains multiple lines with minimal whitespace)
        if '\n' in stripped_text and not stripped_text.strip().startswith('```'):
            # Preserve exact line breaks for code blocks
            lines = stripped_text.split('\n')
            # If there are multiple lines and they look like code (not just paragraphs)
            if len(lines) > 2 and any('def ' in line or 'import ' in line or 'class ' in line for line in lines):
                # This is likely code, preserve exact formatting
                self.insertPlainText(stripped_text)
                return

        # For non-code text, clean it as usual
        cleaned_text = self.clean_text_for_submission(stripped_text)
        self.insertPlainText(cleaned_text)

    def clean_text_for_submission(self, text):
        """Clean text by removing formatting while preserving paragraph structure and code blocks"""
        if not text:
            return text

        # Check if this looks like code (contains multiple lines with minimal whitespace)
        lines = text.split('\n')
        if len(lines) > 2 and any('def ' in line or 'import ' in line or 'class ' in line for line in lines):
            # This is likely code, preserve exact formatting including line breaks
            return text

        # For non-code text, clean it as usual
        paragraphs = re.split(r'\n\s*\n', text)
        cleaned_paragraphs = []

        for paragraph in paragraphs:
            lines = paragraph.split('\n')
            cleaned_lines = []

            for line in lines:
                line = re.sub(r'^[*\-\•\+]\s+', '', line)
                line = re.sub(r'[\u2022\u2023\u25E6\u2043\u25AA\u25AB\u25CF\u25C9\u25CE\u25C8\u25C6]+', '', line)
                line = re.sub(r'[_*~`#]+(.*?)[_*~`#]+', r'\1', line)
                line = re.sub(r'^\d+\.\s+', '', line)
                line = re.sub(r'^[a-zA-Z][.)]\s+', '', line)
                line = re.sub(r'^>\s+', '', line)
                line = re.sub(r'<[^>]*>', '', line)
                if line.strip():
                    cleaned_lines.append(line)
            if cleaned_lines:
                cleaned_paragraphs.append('\n'.join(cleaned_lines))
        result = '\n\n'.join(cleaned_paragraphs)
        return result

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Return and event.modifiers() == Qt.ShiftModifier:
            cursor = self.textCursor()
            cursor.insertText("\n")
            self.adjust_height()
            QTimer.singleShot(1, self.ensure_cursor_visible)
        elif event.key() == Qt.Key_Return:
            # Clean text before submission
            text = self.toPlainText()
            cleaned_text = self.clean_text_for_submission(text)
            self.setText(cleaned_text)

            self.parent_window.display_text()
            self.clear()
            self.setFixedHeight(self.min_height)
        else:
            super().keyPressEvent(event)
            self.adjust_timer.start(10)
            text = self.toPlainText()
            text_without_bullets = re.sub(r'^[*\•]\s+', '', text, flags=re.MULTILINE) # Remove bullets but preserve dashes
            text_cleaned = re.sub(r'[\u2022\u2023\u25E6\u2043\u25AA\u25AB\u25CF\u25C9\u25CE\u25C8\u25C6]+', '', text_without_bullets) # Remove list symbols

            if text != text_cleaned: # Check if text was modified
                cursor_pos = self.textCursor().position() # Store current cursor position
                self.setText(text_cleaned) # Update textedit with cleaned text
                cursor = self.textCursor()
                cursor.setPosition(min(cursor_pos, len(text_cleaned))) # Restore cursor position
                self.setTextCursor(cursor)
            self.format_urls()

            url_pattern = re.compile(r'http[s]?://\S+')
            if not self.space_after_url_inserted and url_pattern.search(text):
                cursor = self.textCursor()
                cursor.setPosition(len(text) )
                cursor.insertText(" ")
                self.space_after_url_inserted = True
            elif not url_pattern.search(text):
                self.space_after_url_inserted = False


# QUOTING CLASS ##################################################################################################################################################################################################################################

class CustomTextBrowser(QTextBrowser):
    define_requested = pyqtSignal(str) # Signal to request definition
    def __init__(self, input_box, scale_font_func, parent=None):
        super().__init__(parent)
        self.input_box = input_box
        self.scale_font = scale_font_func
        self.reaction_button = None
        self.create_reaction_button()
        self.setAcceptDrops(True)  # Enable drop events

        # Connect the anchorClicked signal to our custom handler
        self.anchorClicked.connect(self.handle_anchor_clicked)

        # Set the text browser to not automatically handle URLs
        # This allows our handler to process them first
        self.setOpenLinks(False)

    def handle_anchor_clicked(self, url):
        # Check if this is our custom copy link
        if url.scheme() == "copy":
            # Extract the ID from the URL, removing the leading slash if present
            block_id = url.path().lstrip('/')
            logger.debug(f"Copy button clicked for block ID: {block_id}")
            self.copy_code_block(block_id)
            # Prevent the default navigation
            return

        # For regular links, let the default handler take over
        QDesktopServices.openUrl(url)

    def copy_code_block(self, block_id):
        """Copy the content of a code block to the clipboard"""
        try:
            # Convert block_id to integer
            block_index = int(block_id)

            # Find all code blocks in the document
            document = self.document()
            cursor = QTextCursor(document)
            cursor.movePosition(QTextCursor.Start)

            # Extract text between the horizontal lines
            code_blocks = []
            in_code_block = False
            current_block = []
            block_count = 0

            while not cursor.atEnd():
                cursor.select(QTextCursor.BlockUnderCursor)
                block_text = cursor.selectedText()

                # Check if this is a horizontal line (our code block delimiter)
                if "---------------------------------------" in block_text:
                    if not in_code_block:
                        # Start of a code block
                        in_code_block = True
                        current_block = []
                    else:
                        # End of a code block
                        in_code_block = False
                        # Only add the block if it has content
                        if current_block:
                            code_blocks.append(current_block)
                            # If this is the block we're looking for, we can stop searching
                            if block_count == block_index:
                                break
                            block_count += 1
                elif in_code_block:
                    # Inside a code block, collect the text
                    current_block.append(block_text)

                cursor.movePosition(QTextCursor.NextBlock)

            # Check if we found the requested block
            if 0 <= block_index < len(code_blocks):
                # Join the lines with newlines and copy to clipboard
                code_text = "\n".join(code_blocks[block_index])

                # Filter out the \u2029 character (paragraph separator)
                code_text = code_text.replace('\u2029', '')

                QApplication.clipboard().setText(code_text)
                logger.debug(f"Code block {block_index} copied to clipboard with \u2029 characters filtered out")
            else:
                logger.error(f"Code block index {block_index} out of range (found {len(code_blocks)} blocks)")
        except Exception as e:
            logger.error(f"Error copying code block: {e}")

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            self.setStyleSheet("""
                QTextBrowser {
                    background-color: #232323;
                    padding: 10px;
                    border: 2px solid #00FFFF;
                    font-size: 18px;
                    font-family: 'Calibri';
                    line-height: 10px;
                    margin: 8px;
                    border-radius: 30px;
                }
            """)
            event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        main_window = self.window()
        if hasattr(main_window, 'output_style'):
            self.setStyleSheet(main_window.output_style)
        super().dragLeaveEvent(event)

    def dropEvent(self, event):
        if event.mimeData().hasUrls():
            main_window = self.window()
            if hasattr(main_window, 'output_style'):
                self.setStyleSheet(main_window.output_style)
            file_paths = [url.toLocalFile() for url in event.mimeData().urls()]
            main_window = self.window()
            if len(file_paths) > 1:
                main_window.current_upload_files = file_paths
                log_message = f"{len(file_paths)} Documents Attached"
                logger.info(log_message)
                main_window.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}</font></b>")
                main_window.show_deep_searching_label()
                main_window.waiting_for_document_context = True
            else:
                file_path = file_paths[0]
                original_file_name = os.path.basename(file_path)
                file_extension = os.path.splitext(file_path)[1].lower()
                main_window.current_upload_file_path = file_path
                main_window.current_upload_file_name = original_file_name
                main_window.current_upload_file_extension = file_extension

                log_message = "Document Uploaded"
                logger.info(log_message)
                main_window.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}: {original_file_name}</font></b>")
                main_window.show_deep_searching_label()
                main_window.waiting_for_document_context = True

            event.acceptProposedAction()

    def create_reaction_button(self):
        self.reaction_button = QPushButton("🤔", self)
        self.reaction_button.setFixedSize(
            self.scale_font(30),
            self.scale_font(30)
        )
        self.reaction_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #2c3e50;
                color: white;
                border: none;
                border-radius: {self.scale_font(15)}px;
                font-size: {self.scale_font(16)}px;
            }}
            QPushButton:hover {{
                background-color: #34495e;
            }}
        """)
        self.reaction_button.clicked.connect(self.quote_selected_text)
        self.reaction_button.hide()

    def mouseMoveEvent(self, event):
        super().mouseMoveEvent(event)
        self.update_reaction_button()

    def mouseReleaseEvent(self, event):
        super().mouseReleaseEvent(event)
        self.update_reaction_button()

    def update_reaction_button(self):
        cursor = self.textCursor()
        if cursor.hasSelection():
            rect = self.cursorRect(cursor)
            global_pos = self.mapToGlobal(rect.topRight())
            local_pos = self.mapFromGlobal(global_pos)
            self.reaction_button.move(
                local_pos.x() + self.scale_font(25),
                local_pos.y()
            )
            self.reaction_button.show()
        else:
            self.reaction_button.hide()

    def quote_selected_text(self):
        cursor = self.textCursor()
        if cursor.hasSelection():
            selected_text = cursor.selectedText()
            quoted_text = f"'{selected_text}' "
            self.input_box.append(quoted_text)
            cursor = self.input_box.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.input_box.setTextCursor(cursor)
            self.input_box.setFocus()
        self.reaction_button.hide()

    def leaveEvent(self, event):
        super().leaveEvent(event)
        self.reaction_button.hide()

    def contextMenuEvent(self, event):
        cursor = self.textCursor()
        if cursor.hasSelection():
            selected_text = cursor.selectedText()
            menu = QMenu(self)
            menu.setStyleSheet(f"""
                QMenu {{
                    background-color: #2c3e50;
                    color: white;
                    border-radius: {self.scale_font(10)}px;
                    padding: {self.scale_font(5)}px;
                }}
                QMenu::item {{
                    background-color: transparent;
                    padding: {self.scale_font(8)}px {self.scale_font(15)}px;
                    border-radius: {self.scale_font(10)}px;
                    font-weight: bold;
                }}
                QMenu::item:selected {{
                    background-color: #444444;
                }}
            """)
            menu.setFont(QFont("Arial", self.scale_font(10)))

            copy_action = QAction("Copy", self)
            copy_action.triggered.connect(lambda: self.copy_text(selected_text))
            menu.addAction(copy_action)
            # copy_action.hovered.connect(lambda: self.clear_selection()) # clear_selection on hover might be disruptive

            quote_action = QAction("Quote", self)
            quote_action.triggered.connect(lambda: self.quote_selected_text()) # Make sure this calls the existing method
            menu.addAction(quote_action)
            # quote_action.hovered.connect(lambda: self.clear_selection())

            # New "Define" action
            define_action = QAction("Define", self)
            define_action.triggered.connect(lambda: self.request_define_text(selected_text))
            menu.addAction(define_action)

            search_action = QAction("Search with Google", self)
            search_action.triggered.connect(lambda: self.google_search(selected_text))
            menu.addAction(search_action)
            # search_action.hovered.connect(lambda: self.clear_selection())

            if self.is_address(selected_text):
                maps_action = QAction("Search on Maps", self)
                maps_action.triggered.connect(lambda: self.maps_search(selected_text))
                menu.addAction(maps_action)
                # maps_action.hovered.connect(lambda: self.clear_selection())

            menu.exec_(event.globalPos())
        else:
            super().contextMenuEvent(event)

    def request_define_text(self, text):
        """Emits a signal to request definition for the selected text."""
        self.define_requested.emit(text)
        self.clear_selection() # Clear selection after action

    def clear_selection(self):
        """Clears the text selection in the QTextBrowser."""
        cursor = self.textCursor()
        cursor.clearSelection()
        self.setTextCursor(cursor)

    def copy_text(self, text):
            """Copies the given text to the clipboard."""
            clipboard = QApplication.clipboard()
            clipboard.setText(text)

    def is_address(self, text):
        """Basic check if the text looks like an address."""
        address_pattern = re.compile(
            r'\b(\d+)\s+([A-Za-z0-9\s#.,-]+)\s+(?:(street|st|avenue|ave|road|rd|lane|ln|boulevard|blvd|drive|dr|place|pl|court|ct)\b)?(?:,\s*([A-Za-z\s]+))?(?:,\s*([A-Za-z]{2}))?\s*(\d{5}(-\d{4})?)?\b',
            re.IGNORECASE
        )
        return bool(address_pattern.search(text))

    def google_search(self, text):
        """Opens the default browser to perform a Google search with the given text."""
        search_url = f"https://www.google.com/search?q={text.replace(' ', '+')}"
        QDesktopServices.openUrl(QUrl(search_url))

    def maps_search(self, text):
        """Opens the default browser to perform a Google Maps search with the given address."""
        maps_url = f"https://www.google.com/maps/place/{text.replace(' ', '+')}"
        QDesktopServices.openUrl(QUrl(maps_url))

# MAIN GUI INTERFACE ##################################################################################################################################################################################################
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.latest_user_input = ""
        # For managing conversation titles and log file uniqueness per session
        self.current_session_log_title = "Veritas_User_Interaction_log" # Default/initial title
        self.current_session_log_suffix = datetime.now().strftime("%Y%m%d%H%M%S%f") # Unique ID for the current log file
        self.db_title_set_for_current_session = False # Tracks if a custom title has been set for the current session


        script_dir = os.path.dirname(os.path.abspath(__file__))
        icon_path = os.path.join(script_dir, "ICON/BRADAI ICON.ico")
        self.setWindowTitle("Veritas")
        self.setWindowIcon(QIcon(icon_path))
        self.resize(self.scale_font(1300), self.scale_font(700))
        self.setStyleSheet("background-color: #1A1A1A; color: #ffffff;")

        self.button_style = f"""
            QPushButton {{
                background-color: #2c3e50;
                color: #ecf0f1;
                border: none;
                border-radius: {self.scale_font(5)}px;
                padding: {self.scale_font(10)}px {self.scale_font(15)}px;
                font-size: {self.scale_font(14)}px;
                font-weight: bold;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            QPushButton:hover {{
                background-color: #34495e;
            }}
            QPushButton:pressed {{
                background-color: #2980b9;
            }}
        """

        self.small_button_style = f"""
            QPushButton {{
                background-color: #404040;
                color: #ecf0f1;
                border: none;
                border-radius: {self.scale_font(15)}px;
                padding: {self.scale_font(5)}px {self.scale_font(10)}px;
                font-size: {self.scale_font(12)}px;
                font-weight: bold;
                text-transform: uppercase;
            }}
            QPushButton:hover {{
                background-color: #717171;
            }}
            QPushButton:pressed {{
                background-color: #2980b9;
            }}
        """

        self.output_style = f"""
            QTextBrowser {{
                background-color: #232323;
                padding: {self.scale_font(10)}px;
                border: {self.scale_font(1)}px solid #232323;
                font-size: {self.scale_font(18)}px;
                font-family: 'Calibri';  /* Set font to Calibri */
                line-height: {self.scale_font(5)}px;  /* Reduced line height for more compact text */
                margin: 8px; /* Remove extra spacing */
                border-radius: {self.scale_font(30)}px;
            }}
        """

        trash_icon_path = os.path.join(script_dir, "ICON/Trash.png")
        trash_hover_icon_path = os.path.join(script_dir, "ICON/Trash Hover.png")
        trash_icon_url = trash_icon_path.replace("\\", "/")
        trash_hover_icon_url = trash_hover_icon_path.replace("\\", "/")
        self.reset_button = QPushButton()
        self.reset_button.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                image: url({trash_icon_url});
            }}
            QPushButton:hover {{
                image: url({trash_hover_icon_url});
            }}
        """)
        self.reset_button.setFixedSize(self.scale_font(40), self.scale_font(40))
        # MODIFICATION: Use lambda to call reset_chat without arguments
        self.reset_button.clicked.connect(lambda: self.reset_chat())


        upload_icon_normal_path = os.path.join(script_dir, "ICON/White Upload.png")
        upload_icon_hover_path = os.path.join(script_dir, "ICON/White Upload Hover.png")
        upload_icon_normal_url = upload_icon_normal_path.replace("\\", "/")
        upload_icon_hover_url = upload_icon_hover_path.replace("\\", "/")
        self.upload_button = QPushButton()
        self.upload_button.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                image: url({upload_icon_normal_url});
            }}
            QPushButton:hover {{
                image: url({upload_icon_hover_url});
            }}
        """)
        self.upload_button.setFixedSize(self.scale_font(40), self.scale_font(40))
        self.upload_button.clicked.connect(self.upload_document)

        send_icon_normal_path = os.path.join(script_dir, "ICON/White Send.png")
        send_icon_hover_path = os.path.join(script_dir, "ICON/White Send Hover.png")
        send_icon_normal_url = send_icon_normal_path.replace("\\", "/")
        send_icon_hover_url = send_icon_hover_path.replace("\\", "/")
        self.send_button = QPushButton()
        self.send_button.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                image: url({send_icon_normal_url});
            }}
            QPushButton:hover {{
                image: url({send_icon_hover_url});
            }}
        """)
        self.send_button.setFixedSize(self.scale_font(40), self.scale_font(40))
        self.send_button.clicked.connect(self.send_message)
        self.input_Box = ResizableTextEdit(self)

        # Input box created

        self.outb = CustomTextBrowser(self.input_Box, self.scale_font, self)
        self.outb.setStyleSheet(self.output_style)
        self.outb.installEventFilter(self)
        self.outb.verticalScrollBar().setSingleStep(20)
        self.outb.setOpenExternalLinks(True)
        self.outb.setAcceptRichText(True)
        self.outb.setAcceptDrops(True)
        self.setAcceptDrops(True)

        scroll_bar_style = f"""
            QScrollBar:vertical, QScrollBar:horizontal {{
                border: none;
                background: #0a0a0a;
                width: {self.scale_font(10)}px;
                height: {self.scale_font(10)}px;
                margin: {self.scale_font(2)}px 0 {self.scale_font(2)}px 0;
                border-radius: {self.scale_font(5)}px;
            }}
            QScrollBar::handle:vertical, QScrollBar::handle:horizontal {{
                background: #363636;
                min-height: {self.scale_font(20)}px;
                min-width: {self.scale_font(20)}px;
                border-radius: {self.scale_font(5)}px;
            }}
            QScrollBar::add-line, QScrollBar::sub-line,
            QScrollBar::up-arrow, QScrollBar::down-arrow,
            QScrollBar::left-arrow, QScrollBar::right-arrow {{
                background: none;
                width: {self.scale_font(0)}px;
                height: {self.scale_font(0)}px;
            }}
            QScrollBar::add-page, QScrollBar::sub-page {{
                background: none;
            }}
        """
        self.outb.setStyleSheet(self.output_style + scroll_bar_style)

        self.header_label = QLabel("")
        self.header_label.setFont(QFont("Consolas", self.scale_font(15), QFont.Bold))
        self.header_label.setStyleSheet("color: #00FFFF;")

        self.title_label = QLabel("Veritas")
        self.title_label.setFont(QFont("Consolas", 24, QFont.Bold))
        self.title_label.setStyleSheet("color: #00FFFF;")
        self.title_label.setAlignment(Qt.AlignCenter)

        top_layout = QHBoxLayout()
        top_layout.addWidget(self.header_label, 1, alignment=Qt.AlignLeft)
        top_layout.addWidget(self.title_label, 2, alignment=Qt.AlignCenter)
        top_layout.addWidget(self.reset_button, 1, alignment=Qt.AlignRight)
        self.deep_searching_label = QLabel("", self)
        self.deep_searching_label.setFont(QFont("Consolas", self.scale_font(12)))
        self.deep_searching_label.setStyleSheet("""
            background-color: #232323;
            color: #00FFFF;
            padding: 8px;
            border-radius: 10px;
            border: 2px solid #ffffff;
            font-weight: bold;
        """)
        self.deep_searching_label.setAlignment(Qt.AlignCenter)
        self.deep_searching_label.setWordWrap(True)
        self.deep_searching_label.setTextFormat(Qt.RichText)  # Enable rich text to support clickable links
        self.deep_searching_label.setOpenExternalLinks(True)  # Allow opening links in external browser
        self.deep_searching_label.installEventFilter(self)  # Install event filter for hover and click events
        self.deep_searching_label.hide()

        self.input_container = QWidget()
        input_container_layout = QVBoxLayout(self.input_container)
        input_container_layout.setContentsMargins(0, 0, 0, 0)
        input_container_layout.setSpacing(self.scale_font(5))
        input_container_layout.addWidget(self.deep_searching_label)
        input_container_layout.addWidget(self.input_Box)

        # Set up the main input layout
        input_layout = QHBoxLayout()
        input_layout.setContentsMargins(self.scale_font(10), 0, self.scale_font(10), self.scale_font(10))
        input_layout.setSpacing(self.scale_font(10))
        input_layout.addStretch(1)
        self.input_Box.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.input_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        input_layout.addWidget(self.input_container, 30)

        self.upload_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        input_layout.addWidget(self.send_button, 0)
        input_layout.addSpacing(self.scale_font(10))
        input_layout.addWidget(self.upload_button, 0)
        input_layout.addSpacing(self.scale_font(10))

        deep_thinking_icon_path = os.path.join(script_dir, "ICON/Veritas Deep Thinking.png")
        deep_thinking_selected_icon_path = os.path.join(script_dir, "ICON/Veritas Deep Thinking Selected.png")
        deep_thinking_icon_url = deep_thinking_icon_path.replace("\\", "/")
        deep_thinking_selected_icon_url = deep_thinking_selected_icon_path.replace("\\", "/")
        self.deep_thinking_button = QPushButton()
        self.deep_thinking_button.setCheckable(True)
        self.deep_thinking_button.setToolTip("Enable Deep Research Mode:\nExpands search depth for more comprehensive results.")
        self.deep_thinking_button.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                image: url({deep_thinking_icon_url});
            }}
            QPushButton:hover:!checked {{
                image: url({deep_thinking_icon_url});
            }}
            QPushButton:checked {{
                image: url({deep_thinking_selected_icon_url});
            }}
        """)
        self.deep_thinking_button.setFixedSize(self.scale_font(40), self.scale_font(40))
        self.deep_thinking_button.clicked.connect(self.toggle_deep_thinking)
        input_layout.addWidget(self.deep_thinking_button, 0)

        # Reasoning button removed as it has no purpose

        self.preferences = self.load_preferences()
        self.deep_thinking_button.setChecked(self.preferences.get("deep_research_enabled", False))
        self.update_button_states()

        input_layout.addStretch(1)

        #=============================================================================
        self.communicator = Communicate()
        self.create_tables = pd.DataFrame(columns=["Role", "message", "timestamp"])
        self.communicator.response_received.connect(self.update_response)
        self.preferences = self.load_preferences()
        self.installEventFilter(self)
        self.separator_timer = QTimer()
        self.update_timer = QTimer()
        self.recent_searches = set()
        self.search_triggered = False
        self.chat_reset_flag = False
        self.loaded_db_path = None
        self.message_history = []

        self.communicator.deep_thinking_log_updated.connect(self._update_deep_thinking_log_slot)
        self.communicator.deep_searching_label_updated.connect(self._update_deep_searching_label_slot)

        self.combined_db_file_path = os.path.join(veritas_docs_dir, "combined.db")
        self.db_file_path = self.combined_db_file_path
        self.db_connection = get_db_connection()
        self.db_connection = None
        self.db_processes = []

        self.current_upload_file_path = None
        self.current_upload_file_name = None
        self.current_upload_file_extension = None
        self.waiting_for_document_context = False

        # Define file extensions for different types
        self.image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"]
        self.audio_extensions = [".mp3", ".wav", ".aac", ".flac", ".ogg"]
        self.video_extensions = [".mp4", ".avi", ".mov", ".mkv"]

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        self.main_content_layout = QVBoxLayout(central_widget)

        self.main_content_layout.addLayout(top_layout)

        #=============================================================================
        output_layout = QHBoxLayout()
        output_layout.addWidget(self.outb)
        self.main_content_layout.addLayout(output_layout)
        self.main_content_layout.addLayout(input_layout)

        # GIF animation code removed

        self.deep_thinking_log_label = QLabel(self.outb)
        self.deep_thinking_log_label.setStyleSheet("""
            background: rgba(40, 40, 40, 150); /* Semi-transparent background */
            color: #ffffff;
            padding: 5px;
            border-radius: 5px;
            font-size: 10px; /* Smaller font size */
        """)
        self.deep_thinking_log_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.deep_thinking_log_label.setWordWrap(True)
        self.deep_thinking_log_label.hide()

        self.setup_settings_panel()
        self.close_button.clicked.connect(self.close_settings)
        self.toggle_button_size = self.scale_font(30)
        self.toggle_button_font_size = self.scale_font(10)

        settings_icon_path = os.path.join(script_dir, "ICON/Setting.png")
        settings_hover_icon_path = os.path.join(script_dir, "ICON/Setting Hover.png")
        settings_icon_url = settings_icon_path.replace("\\", "/")
        settings_hover_icon_url = settings_hover_icon_path.replace("\\", "/")
        self.toggle_button = QPushButton()
        self.toggle_button.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                image: url({settings_icon_url});
            }}
            QPushButton:hover {{
                image: url({settings_hover_icon_url});
            }}
        """)
        self.toggle_button.setFixedSize(self.scale_font(40), self.scale_font(40))
        self.toggle_button.clicked.connect(self.toggle_settings)
        self.toggle_button.move(self.scale_font(15), self.scale_font(10))
        self.toggle_button.setParent(self)
        self.toggle_button.setGraphicsEffect(QGraphicsOpacityEffect())
        self.toggle_button.show()

        # GIF overlay positioning code removed

        self.deep_thinking_log_label.move(self.toggle_button.x() + self.toggle_button.width() + self.scale_font(10), self.scale_font(5))
        self.deep_thinking_log_label.setParent(self)

        self.deep_thinking_log_label.setMaximumWidth(self.width() - self.deep_thinking_log_label.x() - self.scale_font(500))
        self.setup_definition_panel() # Initialize the definition panel
        self.outb.define_requested.connect(self.handle_define_request)
        self.deep_thinking_log_label.setFixedHeight(self.scale_font(50))

        # MainWindow.__init__ complete

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.settings_box.setFixedHeight(self.height())
        self.deep_thinking_log_label.setMaximumWidth(self.width() - self.deep_thinking_log_label.x() - self.scale_font(500))
        
        # Adjust definition panel on resize
        if hasattr(self, 'definition_panel') and self.definition_panel.isVisible():
            self.definition_panel.setFixedHeight(self.height())
            # Use the stored definition_panel_width attribute
            self.definition_panel.setFixedWidth(self.definition_panel_width) 
            self.definition_panel.move(self.width() - self.definition_panel_width, 0)
        self.deep_thinking_log_label.setFixedHeight(self.scale_font(50))
        self.deep_thinking_log_label.move(self.toggle_button.x() + self.toggle_button.width() + self.scale_font(10), self.scale_font(5))

        # Update all panels to match the main window size
        panels = [
            'preferences_panel',
            'memory_panel',
            # 'definition_panel', # Removed: Handled specifically above
            'terms_panel',
            'api_key_panel',
            'updates_panel' # Added: updates_panel is also a full-screen panel
        ]

        for panel_name in panels:
            if hasattr(self, panel_name):
                panel = getattr(self, panel_name)
                # Ensure panel is not None and is a QWidget
                if panel and isinstance(panel, QWidget) and panel.isVisible():
                    panel.setFixedSize(self.width(), self.height())
                    # If panel is visible and not animating, keep it at position 0
                    if not hasattr(panel, 'animation') or not panel.animation or panel.animation.state() != QAbstractAnimation.Running:
                        panel.move(0, 0)

        # Resize history_list within settings_box if visible
        if hasattr(self, 'history_list') and hasattr(self, 'settings_box') and self.settings_box.isVisible():
            if hasattr(self, 'close_button') and hasattr(self, 'padding') and \
               hasattr(self, 'button_font_size') and hasattr(self, 'button_spacing') and \
               hasattr(self, 'bottom_spacer_size'):

                # Estimate height of the button section (4 buttons + 1 progress bar + spacing)
                # This is an approximation. A more robust method would involve querying layout managers.
                estimated_button_section_height = self.scale_font(230) 

                available_height = self.height() - (
                    self.close_button.sizeHint().height() +
                    self.padding * 3 + 
                    estimated_button_section_height +
                    self.bottom_spacer_size
                )
                # Correctly resize the history_list
                self.history_list.setFixedHeight(max(self.scale_font(100), available_height))

# EXTRA FEATURES ################################################################################################################################################################################################
    def setup_definition_panel(self):
        """Sets up the panel for displaying definitions."""
        self.definition_panel = QWidget(self)
        self.definition_panel.setStyleSheet("background-color: #1e1e1e; color: #ffffff; border-left: 1px solid #444444;")
        
        # Initial size and position (off-screen to the right)
        self.definition_panel_width = self.scale_font(450) # Consistent width
        self.definition_panel.setFixedSize(self.definition_panel_width, self.height())
        self.definition_panel.move(self.width(), 0)

        layout = QVBoxLayout(self.definition_panel)
        layout.setSpacing(self.scale_font(10))
        layout.setContentsMargins(self.scale_font(15), self.scale_font(15), self.scale_font(15), self.scale_font(15))

        # Top layout for title and close button
        top_layout = QHBoxLayout()
        title_label = QLabel("Definition")
        title_label.setFont(QFont("Consolas", self.scale_font(18), QFont.Bold))
        title_label.setStyleSheet("color: #00FFFF;")
        top_layout.addWidget(title_label)
        top_layout.addStretch(1)

        close_button = QPushButton("✕") # Using a simple '✕' for close
        close_button.setFixedSize(self.scale_font(30), self.scale_font(30))
        close_button.setStyleSheet(f"""
            QPushButton {{ 
                background-color: #333; color: white; 
                border: none; border-radius: {self.scale_font(15)}px; 
                font-size: {self.scale_font(14)}px; font-weight: bold;
            }}
            QPushButton:hover {{ background-color: #555; }}
            QPushButton:pressed {{ background-color: #222; }}
        """)
        close_button.clicked.connect(self.close_definition_panel)
        top_layout.addWidget(close_button)
        layout.addLayout(top_layout)

        # Content area for definition
        self.definition_content_area = QTextBrowser(self)
        # Apply the main output style and the custom scrollbar style
        scroll_bar_style = f"""
            QScrollBar:vertical, QScrollBar:horizontal {{
                border: none;
                background: #0a0a0a;
                width: {self.scale_font(10)}px;
                height: {self.scale_font(10)}px;
                margin: {self.scale_font(2)}px 0 {self.scale_font(2)}px 0;
                border-radius: {self.scale_font(5)}px;
            }}
            QScrollBar::handle:vertical, QScrollBar::handle:horizontal {{
                background: #363636;
                min-height: {self.scale_font(20)}px;
                min-width: {self.scale_font(20)}px;
                border-radius: {self.scale_font(5)}px;
            }}
            QScrollBar::add-line, QScrollBar::sub-line,
            QScrollBar::up-arrow, QScrollBar::down-arrow,
            QScrollBar::left-arrow, QScrollBar::right-arrow {{
                background: none;
                width: {self.scale_font(0)}px;
                height: {self.scale_font(0)}px;
            }}
            QScrollBar::add-page, QScrollBar::sub-page {{
                background: none;
            }}
        """
        self.definition_content_area.setStyleSheet(self.output_style + scroll_bar_style)
        self.definition_content_area.setOpenExternalLinks(True)
        layout.addWidget(self.definition_content_area)

        self.definition_panel.hide()
        self.communicator.definition_received.connect(self.update_definition_panel_content)

    def handle_define_request(self, selected_text):
        """Handles the request to define selected text."""
        self.definition_panel.setFixedSize(self.definition_panel_width, self.height()) # Ensure correct size
        self.definition_panel.move(self.width(), 0) # Ensure it's off-screen before animation
        self.definition_panel.show()
        self.animate_panel(self.definition_panel, self.width(), self.width() - self.definition_panel_width)
        self.definition_content_area.setHtml("<p style='color: #ccc; font-style: italic;'>Loading definition...</p>")

        # Prepare prompt for AI
        prompt = (
            f"You are VERITAS, an advanced AI assistant. A user has highlighted the following text "
            f"and is asking for a definition or explanation. Please provide a clear, concise, and "
            f"informative explanation of the text. If it's a term, define it. If it's a concept, "
            f"explain it. If it's a phrase, clarify its meaning and context.\n\n"
            f"Highlighted Text:\n---\n{selected_text}\n---\n\nYour explanation:"
        )
        threading.Thread(target=self.generate_definition_for_panel, args=(prompt, selected_text), daemon=True).start()

    def generate_definition_for_panel(self, prompt, original_text):
        """Generates definition using AI and emits signal."""
        try:
            # We don't need full conversation history for a simple definition
            ai_response_text = generate_response(prompt, []) 
            self.communicator.definition_received.emit(ai_response_text)
        except Exception as e:
            logger.error(f"Error generating definition: {e}", exc_info=True)
            self.communicator.definition_received.emit(f"<p style='color: red;'>Error: Could not load definition. {str(e)}</p>")

    def update_definition_panel_content(self, ai_response_text):
        """Updates the definition panel with the AI's response."""
        # The AI response might already contain /DBtitle, remove it
        cleaned_response = self._handle_db_title_command(ai_response_text)
        cleaned_response = remove_ai_name_mentions(cleaned_response)
        formatted_definition = self._format_response(cleaned_response)
        self.definition_content_area.setHtml(formatted_definition)

    def close_definition_panel(self):
        """Closes the definition panel with animation."""
        if hasattr(self, 'definition_panel') and self.definition_panel.isVisible():
            self.animate_panel(self.definition_panel, self.definition_panel.x(), self.width())

    def send_message(self):
        self.display_text()
        self.input_Box.clear()
        self.input_Box.setFixedHeight(self.input_Box.min_height)

    def update_placeholder_text(self):
        """Update the placeholder text in the input box."""
        new_placeholder = self.get_greeting_placeholder()
        self.input_Box.setPlaceholderText(new_placeholder)

    def get_greeting_placeholder(self):
        """Generates a dynamic greeting placeholder based on time and user's name."""
        current_hour = datetime.now().hour
        name = ""

        memories = load_mind()
        for alert, response in memories:
            if "Name:" == response[:5] or "User Name:" == response[:10]:
                try:
                    if "Name:" == response[:5]:
                        name = response.split(":")[1].strip()
                    elif "User Name:" == response[:10]:
                        name = response.split(":")[1].strip()
                    break
                except IndexError:
                    pass

        if 0 <= current_hour < 6:
            greeting = "Good Early Morning"
        elif 6 <= current_hour < 12:
            greeting = "Good Morning"
        elif 12 <= current_hour < 17:
            greeting = "Good Afternoon"
        else:
            greeting = "Good Evening"

        if name:
            return f"{greeting} {name}, How may I help you today?"
        else:
            return f"{greeting}, How may I help you today?"

    def eventFilter(self, obj, event):
        # Handle deep searching label hover and click events
        if obj == self.deep_searching_label:
            # Only apply hover and click effects for document analysis, not for search operations
            is_document_context = hasattr(self, 'waiting_for_document_context') and self.waiting_for_document_context

            if event.type() == QEvent.Enter:  # Mouse hover enter
                if is_document_context:
                    # Change border color to red on hover only for document analysis
                    self.deep_searching_label.setStyleSheet("""
                        background-color: #232323;
                        color: #00FFFF;
                        padding: 8px;
                        border-radius: 10px;
                        border: 2px solid #FF0000;
                        font-weight: bold;
                    """)
                return True
            elif event.type() == QEvent.Leave:  # Mouse hover leave
                # Restore original border color
                self.deep_searching_label.setStyleSheet("""
                    background-color: #232323;
                    color: #00FFFF;
                    padding: 8px;
                    border-radius: 10px;
                    border: 2px solid #ffffff;
                    font-weight: bold;
                """)
                return True
            elif event.type() == QEvent.MouseButtonPress:  # Mouse click
                # Only handle clicks for document upload, not for search operations
                if is_document_context:
                    # Reset file attributes
                    self.current_upload_file_path = None
                    self.current_upload_file_name = None
                    self.current_upload_file_extension = None
                    self.current_upload_files = None
                    self.waiting_for_document_context = False

                    # Hide the label
                    self.hide_deep_searching_label()

                    # Restore original placeholder text
                    self.update_placeholder_text()

                    # Log the action
                    logger.info("Document upload canceled by user clicking on the label")
                    return True
                else:
                    # For search operations, ignore clicks
                    return True

        # Handle other events
        if event.type() == QEvent.MouseButtonPress:
            if obj == self or obj == self.outb:
                self.input_Box.setFocus()
                return True
            elif isinstance(obj, (QPushButton, QScrollBar, QComboBox)):
                return False
        if event.type() == QEvent.KeyPress:
            key = event.key()
            modifiers = event.modifiers()
            ignore_keys = (
                Qt.Key_Control,
                Qt.Key_Shift,
                Qt.Key_Alt,
                Qt.Key_Meta,
                Qt.Key_Tab,
                Qt.Key_Enter,
                Qt.Key_Return,
                Qt.Key_Escape,
                Qt.Key_Up,
                Qt.Key_Down,
                Qt.Key_Left,
                Qt.Key_Right,
                Qt.Key_Backspace,
                Qt.Key_Delete,
            )

            if key in ignore_keys:
                return False
            if modifiers & Qt.ControlModifier and (key == Qt.Key_C or key == Qt.Key_V or key == Qt.Key_X or key == Qt.Key_A):
                event.accept()
                return False
            self.input_Box.setFocus()
            if event.text():
                self.input_Box.insertPlainText(event.text())
                return True

        if event.type() == QEvent.KeyRelease:
            if event.key() == Qt.Key_Control and (self.input_Box.hasFocus()):
                return False
            else:
                return False
        return super().eventFilter(obj, event)

# SETTINGS PANEL ##################################################################################################################################################################################################
    def setup_settings_panel(self):
        self.settings_box_size = QSize(self.scale_font(550), self.scale_font(700))
        self.close_button_size = QSize(self.scale_font(100), self.scale_font(50))
        self.close_button_font_size = self.scale_font(14)
        self.settings_label_font_size = self.scale_font(24)
        self.button_font_size = self.scale_font(15)
        self.button_spacing = self.scale_font(12)
        self.bottom_spacer_size = self.scale_font(20)
        self.padding = self.scale_font(15)

        self.settings_box = QWidget()
        self.settings_box.setStyleSheet(f"""
            QWidget {{
                background: #171717;
                border-radius: 0px;
            }}
        """)
        self.settings_box.setFixedSize(self.settings_box_size)

        self.settings_box.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        self.settings_layout = QVBoxLayout(self.settings_box)
        self.settings_layout.setContentsMargins(self.padding, self.padding, self.padding, self.padding)

        self.close_button = self.create_button("Close", self.close_button_font_size,
                                            size=self.close_button_size, color="rgba(220, 53, 69, 01)",
                                            hover_color="rgba(220, 53, 69, 1)")
        self.close_button.clicked.connect(self.toggle_settings)
        self.settings_layout.addWidget(self.close_button, alignment=Qt.AlignRight)

        self.history_list = QListWidget()
        self.history_list.setStyleSheet(f"""
            QListWidget {{
                background: rgba(30, 30, 30, 230); /* Changed to a solid darker color */
                color: #ffffff;
                border: 1px solid #666;
                border-radius: 10px; /* Removed border-radius to match box style */
            }}
            QListWidget::item {{
                padding: {self.scale_font(10)}px;
                border-bottom: 1px solid #888;
            }}
            QListWidget::item:selected {{
                background-color: #545454;
                color: #000;
            }}
            QScrollBar {{
                width: 0px;  /* This hides the scrollbar */
                background: transparent;
            }}
        """)
        self.history_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.load_history_files()
        self.history_list.itemClicked.connect(self.load_selected_history)

        self.settings_layout.addWidget(self.history_list)

        button_layout = QVBoxLayout()
        button_layout.setSpacing(self.button_spacing)

        settings_buttons = [
            (" Change API Key", self.change_api_key),
            (" Terms and Conditions", self.show_terms_and_conditions),
            (" Preferences", self.open_preferences_dialog),
             (" Check for Updates", self.check_for_updates)
        ]

        for text, func in settings_buttons:
            button = self.create_button(text, self.button_font_size, color="rgba(50, 50, 50, 1)", hover_color="rgba(40, 40, 40, 1)")
            button.clicked.connect(func)
            button_layout.addWidget(button)
            if text == " Check for Updates":
                self.update_progress = QProgressBar()
                self.update_progress.setStyleSheet("""
                    QProgressBar {
                        border: 2px solid grey;
                        border-radius: 5px;
                        text-align: center;
                        color: white;
                        background-color: #333;
                    }
                    QProgressBar::chunk {
                        background-color: #00c8ff;
                        border-radius: 5px;
                    }
                """)
                self.update_progress.setFixedHeight(self.scale_font(20))
                self.update_progress.setTextVisible(True)
                self.update_progress.hide()
                button_layout.addWidget(self.update_progress)


        self.settings_layout.addLayout(button_layout)

        bottom_spacer = QSpacerItem(self.bottom_spacer_size, self.bottom_spacer_size,
                                    QSizePolicy.Minimum, QSizePolicy.Fixed)
        self.settings_layout.addItem(bottom_spacer)

        self.settings_box.move(-self.settings_box.width(), 0)
        self.settings_box.setParent(self)

    def create_button(self, text, font_size, size=None, color="rgba(50, 50, 50, 1)", hover_color="rgba(70, 70, 70, 1)"):
        button = QPushButton(text)
        button.setFont(QFont("Arial", font_size))
        if size:
            button.setFixedSize(size)
        button.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                border-radius: {self.scale_font(10)}px;
                padding: {self.scale_font(12)}px;
            }}
            QPushButton:hover {{
                background: {hover_color};
            }}
        """)
        return button

    def show_settings(self):
        self.animate_settings(-self.settings_box.width(), 0)
        self.toggle_button.hide()

    def close_settings(self):
        current_x = self.settings_box.x()
        self.animate_settings(current_x, -self.settings_box.width())
        QTimer.singleShot(500, self.animate_settings_button)
        self.update_progress.hide()

    def toggle_settings(self):
        current_x = self.settings_box.x()

        if current_x < 0:
            target_x = 0
            self.toggle_button.hide()
            self.load_history_files()
        else:
            target_x = -self.settings_box.width()
            QTimer.singleShot(600, self.show_toggle_button)

        self.animate_settings(current_x, target_x)

# API KEY FOR SECURITY ##################################################################################################################################################################################################

    def change_api_key(self):
        # Create API key panel if it doesn't exist yet
        if not hasattr(self, 'api_key_panel'):
            self.api_key_panel = QWidget(self)
            self.api_key_panel.setStyleSheet("background-color: #1e1e1e; color: #ffffff;")

            # Set initial size and position (off-screen to the right)
            # Use full window size
            self.api_key_panel.setFixedSize(self.width(), self.height())
            self.api_key_panel.move(self.width(), 0)

            # Create layout
            layout = QVBoxLayout(self.api_key_panel)
            layout.setSpacing(self.scale_font(10))
            layout.setContentsMargins(self.scale_font(20), self.scale_font(20), self.scale_font(20), self.scale_font(20))

            # Add title and close button at the top in a horizontal layout
            top_layout = QHBoxLayout()

            # Add title
            title_label = QLabel("Change API Key")
            title_label.setFont(QFont("Consolas", self.scale_font(24), QFont.Bold))
            title_label.setStyleSheet("color: #00FFFF;")
            top_layout.addWidget(title_label, alignment=Qt.AlignLeft)

            # Add spacer to push close button to the right
            top_layout.addStretch(1)

            # Add close button
            close_button = QPushButton("Close")
            close_button.setStyleSheet(self.scale_button_style())
            close_button.clicked.connect(self.close_api_key_panel)
            top_layout.addWidget(close_button, alignment=Qt.AlignRight)

            layout.addLayout(top_layout)

            # Add a horizontal line separator
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            line.setStyleSheet("background-color: #444444;")
            line.setFixedHeight(2)
            layout.addWidget(line)
            layout.addSpacing(self.scale_font(20))

            # Create a centered container for the API key input
            center_container = QWidget()
            center_layout = QVBoxLayout(center_container)
            center_layout.setContentsMargins(self.scale_font(100), self.scale_font(50), self.scale_font(100), self.scale_font(50))

            # Add API key input field
            self.api_key_input = QLineEdit()
            self.api_key_input.setPlaceholderText("Enter new API key")
            self.api_key_input.setFont(QFont("Consolas", self.scale_font(12)))
            self.api_key_input.setStyleSheet("""
                QLineEdit {
                    background-color: #2f2f2f;
                    color: #ffffff;
                    padding: 10px;
                    border-radius: 5px;
                    border: 1px solid #3a3a3a;
                }
            """)
            center_layout.addWidget(self.api_key_input)
            center_layout.addSpacing(self.scale_font(20))

            # Add update button
            update_button = QPushButton("Update and Restart")
            update_button.clicked.connect(self.update_api_key_and_restart)
            update_button.setStyleSheet(self.scale_button_style())
            center_layout.addWidget(update_button, alignment=Qt.AlignCenter)

            # Add the centered container to the main layout
            layout.addWidget(center_container)
            layout.addStretch(1)  # Push everything to the top

        # Make sure panel size matches the main window
        self.api_key_panel.setFixedSize(self.width(), self.height())

        # Clear any previous input
        if hasattr(self, 'api_key_input'):
            self.api_key_input.clear()

        # Show and animate the API key panel
        self.api_key_panel.show()
        self.animate_panel(self.api_key_panel, self.width(), 0)  # Animate to position 0 (full screen)

    def close_api_key_panel(self):
        # Animate the API key panel back off-screen to the right
        if hasattr(self, 'api_key_panel'):
            self.animate_panel(self.api_key_panel, 0, self.width())

    def update_api_key_and_restart(self):
        if hasattr(self, 'api_key_input'):
            new_api_key = self.api_key_input.text()
            self.update_api_key(new_api_key)
            self.close_api_key_panel()
            QMessageBox.information(self, "Restarting", "API key updated. The application will now restart.")
            QCoreApplication.quit()
            QProcess.startDetached(sys.executable, sys.argv)



    def update_api_key(self, new_api_key):
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("UPDATE settings SET value = ? WHERE key = 'api_key'", (new_api_key,))
            conn.commit()
        logger.info("API key updated successfully and saved to database.")

# TERMS AND CONDITIONS ##################################################################################################################################################################################################

    def show_terms_and_conditions(self, require_acceptance=False):
        # Create terms panel if it doesn't exist yet
        if not hasattr(self, 'terms_panel'):
            self.terms_panel = QWidget(self)
            self.terms_panel.setStyleSheet("background-color: #1e1e1e; color: #ffffff;")

            # Set initial size and position (off-screen to the right)
            # Use full window size
            self.terms_panel.setFixedSize(self.width(), self.height())
            self.terms_panel.move(self.width(), 0)

            # Create layout
            layout = QVBoxLayout(self.terms_panel)
            layout.setSpacing(self.scale_font(10))
            layout.setContentsMargins(self.scale_font(20), self.scale_font(20), self.scale_font(20), self.scale_font(20))

            # Add title and close button at the top in a horizontal layout
            top_layout = QHBoxLayout()

            # Add title
            title_label = QLabel("Terms and Conditions")
            title_label.setFont(QFont("Consolas", self.scale_font(24), QFont.Bold))
            title_label.setStyleSheet("color: #00FFFF;")
            top_layout.addWidget(title_label, alignment=Qt.AlignLeft)

            # Add spacer to push close button to the right
            top_layout.addStretch(1)

            # Add close button (only show if not requiring acceptance)
            self.terms_close_button = QPushButton("Close")
            self.terms_close_button.setStyleSheet(self.scale_button_style())
            self.terms_close_button.clicked.connect(self.close_terms_panel)
            top_layout.addWidget(self.terms_close_button, alignment=Qt.AlignRight)
            if require_acceptance:
                self.terms_close_button.hide()

            layout.addLayout(top_layout)

            # Add a horizontal line separator
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            line.setStyleSheet("background-color: #444444;")
            line.setFixedHeight(2)
            layout.addWidget(line)
            layout.addSpacing(self.scale_font(20))

            # Create scroll area for terms content
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("QScrollArea { border: none; }")

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setContentsMargins(self.scale_font(10), self.scale_font(10), self.scale_font(10), self.scale_font(10))

            terms_text = QTextEdit()
            terms_text.setReadOnly(True)
            terms_text.setStyleSheet("background-color: #2f2f2f; border: none; padding: 10px;")
            terms_text.setFont(QFont("Consolas", self.scale_font(12)))

            terms_content = """
            <h2>Terms and Conditions of Use</h2>

            <p>Welcome to Veritas, your Personal Assistant! This advanced AI-powered tool is designed to assist you with your queries, provide information, and support your daily tasks through sophisticated cognitive processing. By using this application, you agree to be bound by these Terms and Conditions of Use.</p>

            <h3>How to Use</h3>

            <ul>
                <li>You need to generate an API key. Please refer to instructions if it is needed.</li>
                <li>Once you have the API key, go to settings and add the API key.</li>
                <li>Type your queries in the input box and press Enter or click the send button.</li>
                <li>Access settings by clicking the settings button in the top-left corner.</li>
            </ul>

            <h3>Advanced AI Capabilities</h3>

            <p>Veritas is powered by a sophisticated AI system with advanced cognitive capabilities including complex systems analysis, strategic verification frameworks, and hyperadvanced predictive modeling. The system is designed to deliver decisive, insightful, and transformative responses that transcend conventional AI limitations.</p>

            <h3>Data Privacy & Storage</h3>

            <p>Your inputs, conversations, and preferences are stored locally on your device in a SQLite database. This data is used to remember your preferences, enhance your interaction with the application, and improve the quality of responses through personalization. All data remains on your device and is not shared with third parties.</p>

            <h3>Core Features</h3>

            <ul>
                <li><strong>Deep Research Mode:</strong> Enable this feature to generate multiple search queries that explore different facets of your topic, providing more comprehensive results.</li>
                <li><strong>Document Analysis:</strong> Upload and analyze documents up to 500 pages with automatic OCR for images and scanned content.</li>
                <li><strong>Image Processing:</strong> Upload images for analysis and description using integrated OCR technology.</li>
                <li><strong>Web Search:</strong> Search for information by including "Search" followed by your topic.</li>
                <li><strong>Memory System:</strong> The application remembers context from previous conversations to provide more relevant responses.</li>
                <li><strong>Browser Integration:</strong> The system can interact with websites to perform tasks on your behalf when requested.</li>
            </ul>

            <h3>Supported File Formats</h3>

            <ul>
                <li><strong>Documents:</strong> PDF, DOC, DOCX, PPT, PPTX, TXT, JSON, CSV, Parquet, XLS, XLSX</li>
                <li><strong>Other Files:</strong> All file types are supported for text extraction except image, audio, and video files</li>
                <li><strong>Excluded Files:</strong> Image files (JPG, JPEG, PNG, BMP, TIFF, GIF), Audio files (MP3, WAV, AAC, FLAC, OGG), and Video files (MP4, AVI, MOV, MKV)</li>
            </ul>

            <h3>Cross Matrix Quantum Tokenization Technology</h3>

            <p>Veritas utilizes proprietary Cross Matrix Quantum Tokenization Technology (CMQT) which enables enhanced conversation memory, sophisticated pattern recognition, and advanced data processing capabilities. This technology allows the system to maintain perfect continuity across interaction sessions and build cumulative understanding that incorporates historical context.</p>

            <h3>Error Handling</h3>

            <ul>
                <li>Errors may occur due to network connectivity issues, invalid API keys, unsupported file formats, or resource limitations.</li>
                <li>Document processing errors may occur with corrupted files, password-protected documents, or when OCR fails to recognize text in images.</li>
                <li>Search operations may time out after 7 seconds if a source is unresponsive.</li>
                <li>If you encounter an error, verify your API key and internet connectivity before trying again.</li>
            </ul>

            <h3>Terms of Service</h3>

            <p>These terms may be updated periodically. Your continued use of the application indicates acceptance of any changes. Please use the application for ethical and legal purposes only. While Veritas strives for accuracy, it may not always provide complete information, and users should verify critical information independently.</p>

            <h3>For more information</h3>

            <p>Application was made by Bradley Bulman</p>
            <p style="color: #ADD8E6;">Email: <a style="color: #ADD8E6;" href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>Copyright (c) 2024 Bradley Bulman. All rights reserved.</p>
            """

            terms_text.setText(terms_content)
            scroll_layout.addWidget(terms_text)
            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            # Add checkbox and agree button at the bottom
            bottom_layout = QHBoxLayout()

            # Add checkbox for agreement
            self.terms_checkbox = QCheckBox("I have read and agree to the Terms and Conditions")
            self.terms_checkbox.setStyleSheet("color: #ffffff; font-size: 14px;")
            self.terms_checkbox.stateChanged.connect(self.terms_checkbox_changed)
            bottom_layout.addWidget(self.terms_checkbox, alignment=Qt.AlignLeft)

            # Add agree button
            self.agree_button = QPushButton("Agree")
            self.agree_button.setStyleSheet("""
                QPushButton {
                    background-color: #555555;
                    color: #ffffff;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:enabled {
                    background-color: #0078d7;
                }
                QPushButton:hover:enabled {
                    background-color: #0086f0;
                }
            """)
            self.agree_button.setEnabled(False)
            self.agree_button.clicked.connect(self.accept_terms)
            bottom_layout.addWidget(self.agree_button, alignment=Qt.AlignRight)

            layout.addLayout(bottom_layout)

        # Make sure panel size matches the main window
        self.terms_panel.setFixedSize(self.width(), self.height())

        # Show/hide agreement controls based on whether acceptance is required
        if hasattr(self, 'terms_checkbox'):
            if require_acceptance:
                self.terms_checkbox.show()
                self.agree_button.show()
                self.terms_close_button.hide()
            else:
                self.terms_checkbox.hide()
                self.agree_button.hide()
                self.terms_close_button.show()

        # Show and animate the terms panel
        self.terms_panel.show()
        self.animate_panel(self.terms_panel, self.width(), 0)  # Animate to position 0 (full screen)

    def terms_checkbox_changed(self, state):
        # Enable/disable the agree button based on checkbox state
        self.agree_button.setEnabled(state == Qt.Checked)

    def accept_terms(self):
        # Save acceptance to preferences
        self.preferences["terms_accepted"] = True
        self.save_preferences()
        # Close the terms panel
        self.close_terms_panel()

    def close_terms_panel(self):
        # Animate the terms panel back off-screen to the right
        if hasattr(self, 'terms_panel'):
            self.animate_panel(self.terms_panel, 0, self.width())

# CLEAR MEMORY FEATURE ##################################################################################################################################################################################################
    def get_scale_factor(self) -> float:
        """Returns a scale factor based on screen DPI."""
        return 1

    def scale_button_style(self, base_font_size=20, base_padding=(12, 24), base_border_radius=10, base_border_width=2):
        font_size = self.scale_font(base_font_size)
        padding_top = self.scale_font(base_padding[0])
        padding_side = self.scale_font(base_padding[1])
        border_radius = self.scale_font(base_border_radius)
        border_width = self.scale_font(base_border_width)

        return f"""
            QPushButton {{
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1,
                    stop:0 #42a5f5, stop:1 #1e88e5
                );
                color: white;
                border-radius: {border_radius}px;
                padding: {padding_top}px {padding_side}px;
                font-weight: bold;
                font-size: {font_size}px;
                border: {border_width}px solid #1976D2;
            }}
            QPushButton:hover {{
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1,
                    stop:0 #64b5f6, stop:1 #2196F3
                );
            }}
            QPushButton:pressed {{
                background-color: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e88e5, stop:1 #1565C0
                );
            }}
            QPushButton:focus {{
                outline: none;
                border-color: #64b5f6;
            }}
        """

    def open_preferences_dialog(self):
        # Create preferences panel if it doesn't exist yet
        if not hasattr(self, 'preferences_panel'):
            self.preferences_panel = QWidget(self)
            self.preferences_panel.setStyleSheet("background-color: #1e1e1e; color: #ffffff;")

            # Set initial size and position (off-screen to the right)
            # Use full window size
            self.preferences_panel.setFixedSize(self.width(), self.height())
            self.preferences_panel.move(self.width(), 0)

            # Create layout
            preferences_layout = QVBoxLayout(self.preferences_panel)
            preferences_layout.setSpacing(self.scale_font(10))
            preferences_layout.setContentsMargins(self.scale_font(20), self.scale_font(20), self.scale_font(20), self.scale_font(20))

            # Add title and close button at the top in a horizontal layout
            top_layout = QHBoxLayout()

            # Add title
            title_label = QLabel("Preferences")
            title_label.setFont(QFont("Consolas", self.scale_font(24), QFont.Bold))
            title_label.setStyleSheet("color: #00FFFF;")
            top_layout.addWidget(title_label, alignment=Qt.AlignLeft)

            # Add spacer to push close button to the right
            top_layout.addStretch(1)

            # Add close button
            close_button = QPushButton("Close")
            close_button.setStyleSheet(self.scale_button_style())
            close_button.clicked.connect(self.close_preferences_dialog)
            top_layout.addWidget(close_button, alignment=Qt.AlignRight)

            preferences_layout.addLayout(top_layout)

            # Add a horizontal line separator
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            line.setStyleSheet("background-color: #444444;")
            line.setFixedHeight(2)
            preferences_layout.addWidget(line)
            preferences_layout.addSpacing(self.scale_font(20))



            # Create a grid layout for buttons (2 columns)
            grid_layout = QGridLayout()
            grid_layout.setSpacing(self.scale_font(15))

            # Add buttons to grid
            buttons = [
                ("View Memories", self.open_memory_window_without_closing_preferences),
                ("Clear Memory", self.confirm_clear_memory),
                ("Clear Conversation History", self.confirm_clear_history)
            ]

            # Add buttons to grid (2 columns)
            for i, (text, func) in enumerate(buttons):
                button = QPushButton(text)
                button.setStyleSheet(self.scale_button_style())
                button.clicked.connect(func)
                row = i // 2  # Integer division for row
                col = i % 2   # Modulo for column
                grid_layout.addWidget(button, row, col)

            preferences_layout.addLayout(grid_layout)
            preferences_layout.addStretch(1)  # Add stretch to push content to the top

        # Make sure panel size matches the main window
        self.preferences_panel.setFixedSize(self.width(), self.height())

        # Close settings panel if it's open
        self.close_settings()

        # Show and animate the preferences panel
        self.preferences_panel.show()
        self.animate_preferences_panel(self.width(), 0)  # Animate to position 0 (full screen)

    def close_preferences_dialog(self):
        try:
            open(preferences_file_path, "a").close()
        except Exception as e:
            logging.error(f"Error creating preferences file: {e}")
        self.save_preferences()

        # Animate the preferences panel back off-screen to the right
        if hasattr(self, 'preferences_panel'):
            self.animate_preferences_panel(0, self.width())

    def open_memory_window_and_close_preferences(self):
        self.close_preferences_dialog()
        self.show_memory_window()

    def open_memory_window_without_closing_preferences(self):
        """Opens the memory window without closing the preferences dialog"""
        self.show_memory_window()

    def show_memory_window(self):
        # Create memory panel if it doesn't exist yet
        if not hasattr(self, 'memory_panel'):
            self.memory_panel = QWidget(self)
            self.memory_panel.setStyleSheet("background-color: #1e1e1e; color: #ffffff;")
            if hasattr(self, 'preferences_panel') and self.preferences_panel.isVisible():
                self.memory_panel.setFixedSize(self.width() // 2, self.height())
                self.memory_panel.move(self.width(), 0)
            else:
                self.memory_panel.setFixedSize(self.width(), self.height())
                self.memory_panel.move(self.width(), 0)

            # Create layout
            layout = QVBoxLayout(self.memory_panel)
            layout.setSpacing(self.scale_font(10))
            layout.setContentsMargins(self.scale_font(20), self.scale_font(20), self.scale_font(20), self.scale_font(20))
            top_layout = QHBoxLayout()

            # Add title
            title_label = QLabel("Memory Database")
            title_label.setFont(QFont("Consolas", self.scale_font(24), QFont.Bold))
            title_label.setStyleSheet("color: #00FFFF;")
            top_layout.addWidget(title_label, alignment=Qt.AlignLeft)

            # Add spacer to push close button to the right
            top_layout.addStretch(1)

            # Add close button
            close_button = QPushButton("Close")
            close_button.setStyleSheet(self.scale_button_style())
            close_button.clicked.connect(self.close_memory_panel)
            top_layout.addWidget(close_button, alignment=Qt.AlignRight)

            layout.addLayout(top_layout)

            # Add a horizontal line separator
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            line.setStyleSheet("background-color: #444444;")
            line.setFixedHeight(2)
            layout.addWidget(line)
            layout.addSpacing(self.scale_font(20))

            # Create memory list widget
            self.memory_list_widget = QListWidget()
            self.memory_list_widget.setStyleSheet(f"""
                QListWidget {{
                    background: rgba(30, 30, 30, 230); /* Changed to a solid darker color */
                    color: #ffffff;
                    border: 1px solid #666;
                    border-radius: 10px; /* Removed border-radius to match box style */
                }}
                QListWidget::item {{
                    padding: {self.scale_font(10)}px;
                    border-bottom: 1px solid #888;
                }}
                QListWidget::item:selected {{
                    background-color: #545454;
                    color: #000;
                }}
                QScrollBar {{
                    width: 0px;  /* This hides the scrollbar */
                    background: transparent;
                }}
            """)
            layout.addWidget(self.memory_list_widget)

            # Add button layout at the bottom
            button_layout = QHBoxLayout()

            delete_button = QPushButton("Delete Selected Memory")
            delete_button.setStyleSheet(self.scale_button_style())
            delete_button.clicked.connect(self.delete_selected_memory)
            button_layout.addWidget(delete_button)

            layout.addLayout(button_layout)

        # Update memory list
        self.memory_list_widget.clear()
        memories = load_mind()
        for alert, response in memories:
            self.memory_list_widget.addItem(f"Alert: {alert}\nResponse: {response}\n")

        # Adjust panel size based on whether preferences is visible
        if hasattr(self, 'preferences_panel') and self.preferences_panel.isVisible():
            # Make the memory panel smaller and position it to the right of preferences
            self.memory_panel.setFixedSize(self.width() // 2, self.height())
        else:
            # Use full window size if preferences is not visible
            self.memory_panel.setFixedSize(self.width(), self.height())

        # Show and animate the memory panel
        self.memory_panel.show()
        if hasattr(self, 'preferences_panel') and self.preferences_panel.isVisible():
            # Animate to position next to preferences panel
            self.animate_panel(self.memory_panel, self.width(), self.width() // 2)
        else:
            # Animate to full screen
            self.animate_panel(self.memory_panel, self.width(), 0)

    def delete_selected_memory(self):
        if not hasattr(self, 'memory_list_widget'):
            return

        selected_item = self.memory_list_widget.currentItem()

        if selected_item:
            item_text = selected_item.text()
            alert = item_text.split("\n")[0].replace("Alert: ", "").strip()
            response = item_text.split("\n")[1].replace("Response: ", "").strip()

            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM mind INDEXED BY idx_mind_alert_response WHERE Alert = ? AND response = ?", (alert, response))
                conn.commit()

            self.memory_list_widget.takeItem(self.memory_list_widget.row(selected_item))

    def close_memory_panel(self):
        # Animate the memory panel back off-screen to the right
        if hasattr(self, 'memory_panel'):
            # If preferences panel is visible, animate to its right edge
            # otherwise animate off-screen
            if hasattr(self, 'preferences_panel') and self.preferences_panel.isVisible():
                self.animate_panel(self.memory_panel, self.width() // 2, self.width())
            else:
                self.animate_panel(self.memory_panel, 0, self.width())

    def confirm_clear_memory(self):
        confirmation = QMessageBox.question(
            self,
            "Confirm Clear Memory",
            "Are you sure you want to clear all memory? This will reset all learned preferences. This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No
        )
        if confirmation == QMessageBox.Yes:
            self.clear_memory()
            if hasattr(self, 'memory_list_widget'):
                self.memory_list_widget.clear()

    def clear_memory(self):
        try:
            with get_db_connection() as conn:
                # Use a more efficient delete operation
                conn.execute("DELETE FROM mind")
                # Vacuum the database to reclaim space and optimize
                conn.execute("VACUUM")

            logger.info("Memory cleared successfully.")
            self.load_history_files()

        except sqlite3.Error as e:
            logger.error(f"Database error while clearing memory: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")


    def confirm_clear_history(self):
        confirmation = QMessageBox.question(
            self,
            "Confirm Clear History",
            "Are you sure you want to clear all conversation history? This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirmation == QMessageBox.Yes:
            self.clear_conversation_history()
            if hasattr(self, 'history_list'):
                self.history_list.clear()

    def clear_conversation_history(self):
        """
        Nuclear option: Completely clears ALL conversations and message history.
        This provides a clean slate by removing all stored conversation data.
        Updated to work with the new UUID-based conversation system.
        """
        try:
            # Clear the display first
            self.outb.clear()

            # Use the nuclear clear database function
            self._clear_database()

            # Reset session variables
            self.current_session_log_title = "Veritas_User_Interaction_log"
            self.current_session_log_suffix = datetime.now().strftime("%Y%m%d%H%M%S%f")
            self.db_title_set_for_current_session = False

            # Update the placeholder text
            self.update_placeholder_text()

            # Refresh the history list to show the clean state
            self.load_history_files()

            logger.info("All conversation history cleared successfully")

        except Exception as e:
            logger.error(f"Error during clear all history: {e}", exc_info=True)
            self.communicator.response_received.emit(
                "<b><font color='red'> >>> Error: Failed to clear conversation history.</font></b>"
            )

    def confirm_clear_reinforced_learning(self):
        confirmation = QMessageBox.question(
            self,
            "Confirm Clear Reinforced Learning",
            "Are you sure you want to clear all reinforced learning history? This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirmation == QMessageBox.Yes:
            self.clear_reinforced_learning_history()

    def clear_reinforced_learning_history(self):
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM reinforced_learning")
                conn.commit()

            logger.info("Reinforced learning history cleared successfully.")

        except sqlite3.Error as e:
            logger.error(f"Database error while clearing reinforced learning history: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")

    def toggle_deep_thinking(self):
        """
        Toggles the deep research mode and updates the button icon.
        """
        is_deep_thinking_enabled = not self.preferences.get("deep_research_enabled", False)
        self.preferences["deep_research_enabled"] = is_deep_thinking_enabled
        self.save_preferences()

        self.deep_thinking_button.setChecked(is_deep_thinking_enabled)
        self.update_button_states()

    # toggle_reasoning_mode method removed as it has no purpose

    def update_button_states(self):
        """
        Updates the button states to reflect the preferences.
        """
        self.save_preferences()
        self.deep_thinking_button.setChecked(self.preferences["deep_research_enabled"])

    def load_preferences(self):
        try:
            with open(preferences_file_path, "r") as file:
                prefs = json.load(file)
        except (FileNotFoundError, json.JSONDecodeError):
            prefs = {}
        if "deep_research_enabled" not in prefs:
            prefs["deep_research_enabled"] = False
        if "terms_accepted" not in prefs:
            prefs["terms_accepted"] = False
        return prefs

    def save_preferences(self):
        self.preferences["deep_research_enabled"] = self.deep_thinking_button.isChecked()
        # Reasoning mode removed as it has no purpose
        with open(preferences_file_path, "w") as file:
            json.dump(self.preferences, file, indent=4)

    def check_terms_acceptance(self):
        # Check if terms have been accepted
        if not self.preferences.get("terms_accepted", False):
            # Show terms and conditions with acceptance required
            self.show_terms_and_conditions(require_acceptance=True)


# CHECK FOR UPDATES ##################################################################################################################################################################################################

    def check_for_updates(self) -> None:
        # Create updates panel if it doesn't exist yet
        if not hasattr(self, 'updates_panel'):
            self.updates_panel = QWidget(self)
            self.updates_panel.setStyleSheet("background-color: #1e1e1e; color: #ffffff;")

            # Set initial size and position (off-screen to the right)
            # Use full window size
            self.updates_panel.setFixedSize(self.width(), self.height())
            self.updates_panel.move(self.width(), 0)

            # Create layout
            updates_layout = QVBoxLayout(self.updates_panel)
            updates_layout.setSpacing(self.scale_font(10))
            updates_layout.setContentsMargins(self.scale_font(20), self.scale_font(20), self.scale_font(20), self.scale_font(20))

            # Add title and close button at the top in a horizontal layout
            top_layout = QHBoxLayout()

            # Add title
            title_label = QLabel("Check for Updates")
            title_label.setFont(QFont("Consolas", self.scale_font(24), QFont.Bold))
            title_label.setStyleSheet("color: #00FFFF;")
            top_layout.addWidget(title_label, alignment=Qt.AlignLeft)

            # Add spacer to push close button to the right
            top_layout.addStretch(1)

            # Add close button
            close_button = QPushButton("Close")
            close_button.setStyleSheet(self.scale_button_style())
            close_button.clicked.connect(self.close_updates_panel)
            top_layout.addWidget(close_button, alignment=Qt.AlignRight)

            updates_layout.addLayout(top_layout)

            # Add a horizontal line separator
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            line.setStyleSheet("background-color: #444444;")
            line.setFixedHeight(2)
            updates_layout.addWidget(line)
            updates_layout.addSpacing(self.scale_font(20))

            # Create a centered container for the update components
            center_container = QWidget()
            center_layout = QVBoxLayout(center_container)
            center_layout.setContentsMargins(self.scale_font(50), self.scale_font(20), self.scale_font(50), self.scale_font(20))

            # Add status text box
            self.update_status_box = QTextBrowser()
            self.update_status_box.setStyleSheet("""
                QTextBrowser {
                    background-color: #2f2f2f;
                    color: #ffffff;
                    border-radius: 5px;
                    border: 1px solid #3a3a3a;
                    padding: 10px;
                }
            """)
            self.update_status_box.setFixedHeight(self.scale_font(150))
            self.update_status_box.setText("Click 'Check for Updates' to check if a new version is available.")
            center_layout.addWidget(self.update_status_box)
            center_layout.addSpacing(self.scale_font(20))

            # Add progress bar
            self.updates_progress = QProgressBar()
            self.updates_progress.setStyleSheet("""
                QProgressBar {
                    border: 2px solid grey;
                    border-radius: 5px;
                    text-align: center;
                    color: white;
                    background-color: #333;
                }
                QProgressBar::chunk {
                    background-color: #00c8ff;
                    border-radius: 5px;
                }
            """)
            self.updates_progress.setFixedHeight(self.scale_font(20))
            self.updates_progress.setTextVisible(True)
            center_layout.addWidget(self.updates_progress)
            center_layout.addSpacing(self.scale_font(20))

            # Add check for updates button
            check_updates_button = QPushButton("Check for Updates")
            check_updates_button.setStyleSheet(self.scale_button_style())
            check_updates_button.clicked.connect(self._start_update_check)
            center_layout.addWidget(check_updates_button, alignment=Qt.AlignCenter)

            # Add the centered container to the main layout
            updates_layout.addWidget(center_container)
            updates_layout.addStretch(1)  # Push everything to the top

        # Make sure panel size matches the main window
        self.updates_panel.setFixedSize(self.width(), self.height())

        # Close settings panel if it's open
        self.close_settings()

        # Reset progress and status
        self.updates_progress.setValue(0)
        self.update_status_box.setText("Click 'Check for Updates' to check if a new version is available.")

        # Show and animate the updates panel
        self.updates_panel.show()
        self.animate_panel(self.updates_panel, self.width(), 0)  # Animate to position 0 (full screen)

    def close_updates_panel(self):
        # Animate the updates panel back off-screen to the right
        if hasattr(self, 'updates_panel'):
            self.animate_panel(self.updates_panel, 0, self.width())

    def _start_update_check(self):
        self.updates_progress.setValue(0)
        self.update_status_box.setText("Checking for updates...")
        update_thread = threading.Thread(target=self._perform_update, daemon=True)
        update_thread.start()

    def _perform_update(self) -> None:
        try:
            self._update_progress(0, "Starting update check...")
            update_url = "https://www.dropbox.com/scl/fi/9tlmuk7gsltzkfhnb52su/VeritasAI_Update.zip?rlkey=js1sxjx2s5g54st5ttzxwlsz3&st=dy0spxw7&dl=1"
            update_zip_path = os.path.join(script_dir, "update_temp.zip")
            extract_path = os.path.join(script_dir, "update_extracted")

            # Determine script path based on OS
            system = platform.system()
            if system == "Windows":
                script_path = os.path.join(script_dir, "update.bat")
            else:
                script_path = os.path.join(script_dir, "update.sh")

            self._update_progress(10, "Checking if update is already downloaded...")
            if self._is_update_already_downloaded(update_url, update_zip_path):
                self._update_progress(40, "Update file already downloaded, skipping download...")
            else:
                self._update_progress(15, "Downloading update file...")
                self._download_file(update_url, update_zip_path)
                self._update_progress(40, "Download complete.")

            self._update_progress(50, "Extracting update files...")
            self._extract_zip(update_zip_path, extract_path)
            logger.info("Extraction complete. Proceeding with update replacement.")
            self._update_progress(70, "Extraction complete. Preparing update script...")

            # Create the update script and get the actual path (may have changed based on OS)
            script_path = self._create_update_bat(script_path, extract_path)
            self._update_progress(90, "Update script prepared. Ready to apply update...")

            self._apply_update(script_path)
        except Exception as e:
            error_message = f"Update process failed: {str(e)}"
            if hasattr(self, 'update_progress'):
                self.update_progress.hide()
            if hasattr(self, 'update_status_box'):
                self.update_status_box.append(f"<font color='red'>{error_message}</font>")
            logger.error(error_message, exc_info=True)

    def _is_update_already_downloaded(self, update_url: str, download_path: str) -> bool:
        if not os.path.exists(download_path):
            return False

        try:
            # Use random user agent to avoid rate limiting
            headers = {
                "User-Agent": get_random_user_agent()
            }

            # Log the user agent being used
            logger.debug(f"Using User-Agent: {headers['User-Agent']} for URL: {update_url}")

            response = requests.head(update_url, headers=headers, timeout=30)
            response.raise_for_status()
            file_size_remote = int(response.headers.get('content-length', 0))
            file_size_local = os.path.getsize(download_path)
            if file_size_local == file_size_remote:
                logger.info("Update file already downloaded and has same size.")
                return True 
            else:
                logger.info("Update file is different. Redownloading...")
                return False
        except requests.RequestException as e:
            logger.error(f"Failed to check update file size: {e}")
        return False

    def _download_file(self, url: str, download_path: str) -> None:
        try:
            # Use random user agent to avoid rate limiting
            headers = {
                "User-Agent": get_random_user_agent()
            }

            # Log the user agent being used
            logger.debug(f"Using User-Agent: {headers['User-Agent']} for URL: {url}")

            response = requests.get(url, stream=True, headers=headers, timeout=30)
            response.raise_for_status()
            with open(download_path, 'wb') as file:
                for chunk in response.iter_content(chunk_size=8192):
                    file.write(chunk)
            logger.info(f"File downloaded successfully to: {download_path}")
        except requests.RequestException as e:
            logger.error(f"Failed to download file from {url}: {e}")
            raise Exception(f"Failed to download update: {e}")

    def _extract_zip(self, zip_path: str, extract_path: str) -> None:
        try:
            os.makedirs(extract_path, exist_ok=True)  # Ensure extraction folder exists
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
            logger.info(f"Zip file extracted successfully to: {extract_path}")
        except zipfile.BadZipFile as e:
            logger.error(f"Failed to extract zip file: {e}")
            raise Exception(f"Failed to extract update: {e}")

    def _create_update_bat(self, bat_file_path: str, extract_path: str) -> str:
        try:
            current_exe = os.path.abspath(sys.executable)
            install_folder = os.path.dirname(current_exe)
            new_update_folder = extract_path

            # Detect operating system
            system = platform.system()
            logger.info(f"Detected operating system: {system}")

            if system == "Windows":
                # Windows batch file
                script_content = f"""@echo off
                timeout /t 2 /nobreak >nul
                taskkill /f /im "{os.path.basename(current_exe)}"
                timeout /t 2 /nobreak >nul
                :: Update _Internal folder: copy only top-level files (skip all subfolders)
                robocopy "{new_update_folder}\\_Internal" "{install_folder}\\_Internal" /MIR /LEV:1 /XD "Veritas Documents" "ICON" "Previous History Logs" "certifi" /XF "update.bat"
                :: Copy new executable to replace the old one
                copy /Y "{new_update_folder}\\Veritas AI.exe" "{install_folder}\\Veritas AI.exe"
                timeout /t 2 /nobreak >nul
                cd /d "{install_folder}"
                start "" "{install_folder}\\Veritas AI.exe"
                exit
                """
                # Use .bat extension for Windows
                if not bat_file_path.endswith('.bat'):
                    bat_file_path = bat_file_path.replace('.sh', '.bat') if bat_file_path.endswith('.sh') else bat_file_path + '.bat'
            else:
                # macOS/Linux bash script
                script_content = f"""#!/bin/bash
                sleep 2
                # Kill the current process
                pkill -f "{os.path.basename(current_exe)}"
                sleep 2
                # Update _Internal folder: copy files but exclude certain directories
                rsync -av --exclude="Veritas Documents" --exclude="ICON" --exclude="Previous History Logs" --exclude="certifi" "{new_update_folder}/_Internal/" "{install_folder}/_Internal/"
                # Copy new executable to replace the old one
                if [[ -f "{new_update_folder}/Veritas AI.app" ]]; then
                    # macOS app bundle
                    cp -R "{new_update_folder}/Veritas AI.app" "{install_folder}/"
                elif [[ -f "{new_update_folder}/Veritas AI" ]]; then
                    # Linux executable
                    cp "{new_update_folder}/Veritas AI" "{install_folder}/"
                    chmod +x "{install_folder}/Veritas AI"
                fi
                sleep 2
                cd "{install_folder}"
                # Start the application based on what exists
                if [[ -d "{install_folder}/Veritas AI.app" ]]; then
                    open "{install_folder}/Veritas AI.app"
                elif [[ -f "{install_folder}/Veritas AI" ]]; then
                    "{install_folder}/Veritas AI" &
                fi
                """
                # Use .sh extension for macOS/Linux
                if not bat_file_path.endswith('.sh'):
                    bat_file_path = bat_file_path.replace('.bat', '.sh') if bat_file_path.endswith('.bat') else bat_file_path + '.sh'

            with open(bat_file_path, 'w') as script_file:
                script_file.write(script_content)

            # Make the script executable on macOS/Linux
            if system != "Windows":
                os.chmod(bat_file_path, 0o755)

            logger.info(f"Update script created successfully: {bat_file_path}")
            return bat_file_path
        except Exception as e:
            logger.error(f"Failed to create update script: {e}")
            raise Exception(f"Failed to create update script: {e}")

    def _apply_update(self, script_path: str) -> None:
        try:
            self._update_progress(100, "Download Complete. Applying update...")
            QApplication.processEvents()

            # Detect operating system
            system = platform.system()

            if system == "Windows":
                # Windows-specific process creation flags
                subprocess.Popen(
                    script_path,
                    shell=True,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.DETACHED_PROCESS
                )
            else:
                # macOS/Linux process creation
                subprocess.Popen(
                    ["bash", script_path],
                    shell=False,
                    start_new_session=True
                )

            # Update status box with success message
            if hasattr(self, 'update_status_box'):
                self.update_status_box.append("<font color='green'>Update successfully downloaded and prepared. Application will restart to apply the update.</font>")

        except Exception as e:
            error_message = f"Failed to execute update process: {e}"
            if hasattr(self, 'update_progress'):
                self.update_progress.hide()
            if hasattr(self, 'update_status_box'):
                self.update_status_box.append(f"<font color='red'>{error_message}</font>")
            logger.error(error_message)

    def _update_progress(self, value: int, text: str = "") -> None:
        # Update both progress bars if they exist
        if hasattr(self, 'update_progress'):
            self.update_progress.setValue(value)
            if text:
                self.update_progress.setFormat(f"{text}")

        if hasattr(self, 'updates_progress'):
            self.updates_progress.setValue(value)
            if text:
                self.updates_progress.setFormat(f"{text}")
                # Also update the status box with more detailed information
                if hasattr(self, 'update_status_box'):
                    self.update_status_box.append(f"{text}")

        QApplication.processEvents()

# SCALING AND EVENTS AND ANIMATIONS ##################################################################################################################################################################################################

    def scale_font(self, size: int) -> int:
        screen = self.screen()
        logical_dpi = screen.logicalDotsPerInch()
        standard_dpi = 96
        scaled_size = int(size * (logical_dpi / standard_dpi))
        return scaled_size


    def mousePressEvent(self, event):
        if hasattr(self, 'settings_box') and self.settings_box.isVisible():
            if not self.settings_box.geometry().contains(event.globalPos()) and not self.outb.geometry().contains(event.globalPos()):
                self.close_settings()

        panels = [
            ('preferences_panel', self.close_preferences_dialog),
            ('memory_panel', self.close_memory_panel),
            ('definition_panel', self.close_definition_panel),
            ('terms_panel', self.close_terms_panel),
            ('api_key_panel', self.close_api_key_panel),
            ('updates_panel', self.close_updates_panel)
        ]

        for panel_name, close_method in panels:
            if hasattr(self, panel_name) and getattr(self, panel_name).isVisible():
                if not getattr(self, panel_name).geometry().contains(event.globalPos()):
                    close_method()
                    break  # Only close one panel at a time

        super().mousePressEvent(event)

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            # Add cyan glow effect to the output box when dragging over
            self.outb.setStyleSheet(self.output_style + """
                QTextBrowser {
                    border: 2px solid #00FFFF;
                }
            """)
            event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        # Remove the glow effect when drag leaves
        self.outb.setStyleSheet(self.output_style)
        super().dragLeaveEvent(event)

    def dropEvent(self, event):
        if event.mimeData().hasUrls():
            # Remove the glow effect
            self.outb.setStyleSheet(self.output_style)

            # Get the file paths from the dropped URLs
            file_paths = [url.toLocalFile() for url in event.mimeData().urls()]

            if len(file_paths) > 1:
                log_message = f"{len(file_paths)} Documents Attached"
                logger.info(log_message)
                # Show message in deep_searching_label
                self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}</font></b>")
                self.show_deep_searching_label()
                self.waiting_for_document_context = True
                self.current_upload_files = file_paths
            else:
                file_path = file_paths[0]
                original_file_name = os.path.basename(file_path)
                file_extension = os.path.splitext(file_path)[1].lower()
                self.current_upload_file_path = file_path
                self.current_upload_file_name = original_file_name
                self.current_upload_file_extension = file_extension

                log_message = "Document Uploaded"
                logger.info(log_message)
                # Show message in deep_searching_label
                self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}: {original_file_name}</font></b>")
                self.show_deep_searching_label()
                self.waiting_for_document_context = True

            event.acceptProposedAction()

    def show_toggle_button(self):
        self.toggle_button.show()

    def animate_settings_button(self):
        self.toggle_button.show()
        opacity_effect = QGraphicsOpacityEffect(self.toggle_button)
        self.toggle_button.setGraphicsEffect(opacity_effect)
        self.toggle_button_animation = QPropertyAnimation(opacity_effect, b"opacity")
        self.toggle_button_animation.setDuration(300)
        self.toggle_button_animation.setStartValue(0)
        self.toggle_button_animation.setEndValue(1)
        self.toggle_button_animation.setEasingCurve(QEasingCurve.InOutCubic)
        self.toggle_button_animation.start()

    def animate_settings(self, start_x, end_x):
        self.animation = QPropertyAnimation(self.settings_box, b"geometry")
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.InOutCubic)
        start_geometry = self.settings_box.geometry()
        end_geometry = QRect(end_x, 0, self.settings_box.width(), self.settings_box.height())
        self.animation.setStartValue(start_geometry)
        self.animation.setEndValue(end_geometry)
        self.animation.start()

    def animate_panel(self, panel, start_x, end_x):
        # Generic method to animate any panel
        animation = QPropertyAnimation(panel, b"pos")
        animation.setDuration(500)  # 500ms animation
        animation.setEasingCurve(QEasingCurve.InOutCubic)  # Smooth animation curve

        # Set start and end positions
        animation.setStartValue(QPoint(start_x, 0))
        animation.setEndValue(QPoint(end_x, 0))

        # Connect finished signal to handle cleanup
        animation.finished.connect(lambda: self.on_panel_animation_finished(panel))

        # Store the animation as an attribute of the panel to prevent garbage collection
        panel.animation = animation

        # Start the animation
        animation.start()

    def on_panel_animation_finished(self, panel):
        # Hide the panel if it's off-screen
        if panel.x() >= self.width():
            panel.hide()

    def animate_preferences_panel(self, start_x, end_x):
        # Use the generic panel animation method
        self.animate_panel(self.preferences_panel, start_x, end_x)

    def show_deep_searching_label(self):
        """Show the deep searching label with animation"""
        # Ensure we're on the main thread
        if QThread.currentThread() != QApplication.instance().thread():
            # If we're not on the main thread, use a signal-slot connection to get there
            logger.warning("show_deep_searching_label called from non-UI thread, redirecting to main thread")
            # Use QTimer.singleShot to call this method on the main thread
            QTimer.singleShot(0, self.show_deep_searching_label)
            return

        if self.deep_searching_label.isVisible():
            return

        # Store original height of input box
        self.original_input_height = self.input_Box.height()

        # Set cursor based on context
        is_document_context = hasattr(self, 'waiting_for_document_context') and self.waiting_for_document_context
        if is_document_context:
            # For document analysis, use pointing hand cursor to indicate it's clickable
            self.deep_searching_label.setCursor(Qt.PointingHandCursor)
        else:
            # For search operations, use standard cursor to indicate it's not clickable
            self.deep_searching_label.setCursor(Qt.ArrowCursor)

        # Show the label
        self.deep_searching_label.show()

        # Create animation for smooth transition
        self.label_animation = QPropertyAnimation(self.input_Box, b"geometry")
        self.label_animation.setDuration(300)  # 300ms animation
        self.label_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Get current geometry
        current_geometry = self.input_Box.geometry()

        # Create new geometry that's the same but with adjusted position
        new_geometry = QRect(
            current_geometry.x(),
            current_geometry.y(),
            current_geometry.width(),
            current_geometry.height()
        )

        # Set animation values
        self.label_animation.setStartValue(current_geometry)
        self.label_animation.setEndValue(new_geometry)
        self.label_animation.start()

    def show_completion_message(self, message):
        """Show completion message and hide after a short delay"""
        # Use signals to update UI from any thread
        self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FF00'> >>> {message} </font></b>")

        # We'll let the _update_deep_searching_label_slot method handle the timer
        # since it will detect this is a completion message

    def hide_deep_searching_label(self):
        """Hide the deep searching label with animation"""
        # Ensure we're on the main thread
        if QThread.currentThread() != QApplication.instance().thread():
            # If we're not on the main thread, use a signal-slot connection to get there
            logger.warning("hide_deep_searching_label called from non-UI thread, redirecting to main thread")
            # Use QTimer.singleShot to call this method on the main thread
            QTimer.singleShot(0, self.hide_deep_searching_label)
            return

        # This method might be called from a QTimer in a different thread
        # We need to ensure it runs on the UI thread
        if not self.deep_searching_label.isVisible():
            return

        # Stop any ongoing animation
        if hasattr(self, 'label_animation_timer') and self.label_animation_timer.isActive():
            self.label_animation_timer.stop()

        # Directly hide the label without animation for completion messages
        # This ensures it always hides properly after the timer expires
        self.deep_searching_label.hide()

        # Reset any ongoing animations
        if hasattr(self, 'label_animation') and self.label_animation is not None:
            if self.label_animation.state() == QPropertyAnimation.Running:
                self.label_animation.stop()

        # Log that we're hiding the label
        logger.debug("Deep searching label hidden")

# SEARCH QUERY USING /SEARCH ##################################################################################################################################################################################################

    def handle_search_query(self, search_input: Union[str, List[str]]) -> None:
        try:
            # Reset the chat_reset_flag when starting a new search
            self.chat_reset_flag = False

            thread = threading.Thread(
                target=self.fetch_and_process_best_result,
                args=(search_input,),
                daemon=True
            )
            thread.start()
        except Exception as e:
            error_message = f"Search operation failed: {str(e)}"
            logger.error(error_message, exc_info=True)
            self.communicator.response_received.emit(f"Error: {error_message}<br><br>")
            # Movie animation code removed

    def _generate_context_prefix(self, last_user_input: str) -> str:
        """
        Generates a context prefix for the AI model, emphasizing a direct answer
        based on the original user input, optionally removing search command prefixes.
        """
        search_phrases = [
            ("can you search for", 17),
            ("please search for", 17),
            ("search for", 11),
            ("search", 7)
        ]

        filtered_user_input = last_user_input.lower()
        user_intent_text = last_user_input # Start with the full input

        # Check if the input starts with a search command and remove it
        for phrase, length in search_phrases:
            if filtered_user_input.startswith(phrase):
                user_intent_text = last_user_input[length:].strip()
                logger.info(f"Removed search phrase '{phrase}', user intent is: '{user_intent_text}'")
                break # Stop after finding the first match

        # Always prioritize providing a direct answer based on the user's original intent
        return f"PRIORITY INSTRUCTION: Provide a direct answer to the user's query or question: {user_intent_text} "

    # Method removed as its functionality is now directly in fetch_and_process_best_result

    def _prepare_search_queries(self, search_input, is_deep_research, is_initial_search=True):
        if isinstance(search_input, list):
            search_queries = search_input
            logger.info(f"Using provided list of search queries: {search_queries}")
        elif isinstance(search_input, str):
            if is_deep_research:
                if is_initial_search:
                    # For initial deep research, only use the exact query
                    search_queries = [search_input]
                    logger.info(f"Using exact search query for initial deep research: {search_queries}")
                else:
                    # For follow-up deep research, use AI-generated queries or fallback to default variations
                    if hasattr(self, 'ai_generated_queries') and self.ai_generated_queries:
                        search_queries = self.ai_generated_queries
                        logger.info(f"Using AI-generated search queries for deep research: {search_queries}")
                    else:
                        # Fallback to default variations if AI generation failed
                        search_queries = [search_input]  # Always include the original query first
                        variations = [
                            f"{search_input} overview",
                            f"{search_input} latest",
                            f"{search_input} details",
                            f"{search_input} information"
                        ]
                        search_queries.extend(variations)
                        logger.info(f"Using default search query variations for deep research: {search_queries}")
            else:
                search_queries = [search_input]
                logger.info(f"Using single search query: {search_queries}")
        else:
            logger.error(f"Invalid search_input type: {type(search_input)}")
            self.communicator.response_received.emit("Error: Invalid search input type.<br><br>")
            return None

        return search_queries

    @pyqtSlot(str)
    def _update_deep_thinking_log_slot(self, message):
        if QThread.currentThread() != QApplication.instance().thread():
            logger.warning("_update_deep_thinking_log_slot called from non-UI thread, redirecting to main thread")
            QTimer.singleShot(0, lambda: self._update_deep_thinking_log_slot(message))
            return

        # Update the deep_thinking_log_label with the message
        self.deep_thinking_log_label.setText(message)
        self.deep_thinking_log_label.show()

    @pyqtSlot(str)
    def _update_deep_searching_label_slot(self, message):
        if QThread.currentThread() != QApplication.instance().thread():
            logger.warning("_update_deep_searching_label_slot called from non-UI thread, redirecting to main thread")
            QTimer.singleShot(0, lambda: self._update_deep_searching_label_slot(message))
            return

        # Show the label if it's not visible
        if not self.deep_searching_label.isVisible():
            self.show_deep_searching_label()

        # Use character-by-character animation for the label text
        self._animate_label_text(message)

        # If this is a completion or error message, set up a timer to hide it
        if ("Complete" in message or "complete" in message or
            "Error" in message or "error" in message):

            # Determine the delay based on message type
            delay = 3000  # Default 3 seconds for completion messages
            if "Error" in message or "error" in message:
                delay = 1000  # 1 second for error messages
                logger.debug(f"Setting up timer to hide error message: {message}")
            else:
                logger.debug(f"Setting up timer to hide completion message: {message}")

            # Cancel any existing timers
            if hasattr(self, 'completion_timer') and self.completion_timer.isActive():
                self.completion_timer.stop()

            # Create a new timer to hide the label
            self.completion_timer = QTimer()
            self.completion_timer.setSingleShot(True)
            self.completion_timer.timeout.connect(self.hide_deep_searching_label)
            self.completion_timer.start(delay)

    def _update_deep_thinking_log(self, message):
        # Update both the deep_thinking_log and deep_searching_label
        self.communicator.deep_thinking_log_updated.emit(message)
        self.communicator.deep_searching_label_updated.emit(message)

    def _animate_label_text(self, message):
        """Animate the label text character by character"""
        # Ensure we're on the main thread
        if QThread.currentThread() != QApplication.instance().thread():
            # If we're not on the main thread, use a signal-slot connection to get there
            logger.warning("_animate_label_text called from non-UI thread, redirecting to main thread")
            # Use QTimer.singleShot to call this method on the main thread
            QTimer.singleShot(0, lambda: self._animate_label_text(message))
            return

        self.full_label_message = message

        html_pattern = re.compile(r'(<[^>]*>)([^<]*)')
        matches = html_pattern.findall(message)

        if not matches:
            self.label_parts = [(None, message)]
        else:
            self.label_parts = []
            for tag, content in matches:
                self.label_parts.append((tag, content))

        self.current_part_index = 0
        self.current_char_index = 0
        self.animation_text = ""

        if not hasattr(self, 'label_animation_timer'):
            self.label_animation_timer = QTimer()
            self.label_animation_timer.timeout.connect(self._animate_next_char)

        self.label_animation_timer.stop()
        self.deep_searching_label.setText("")
        self.label_animation_timer.start(15)  # 15ms per character (50 chars/second)

    def _animate_next_char(self):
        """Add the next character to the animated text"""
        if QThread.currentThread() != QApplication.instance().thread():
            logger.warning("_animate_next_char called from non-UI thread, redirecting to main thread")
            QTimer.singleShot(0, self._animate_next_char)
            return

        if self.current_part_index >= len(self.label_parts):
            self.label_animation_timer.stop()
            return

        tag, content = self.label_parts[self.current_part_index]

        if self.current_char_index == 0 and tag:
            self.animation_text += tag

        if self.current_char_index < len(content):
            self.animation_text += content[self.current_char_index]
            self.current_char_index += 1
            self.deep_searching_label.setText(self.animation_text)
        else:
            if tag:
                if '<b>' in tag:
                    self.animation_text += '</b>'
                if '<font' in tag:
                    self.animation_text += '</font>'
                if '<strong' in tag:
                    self.animation_text += '</strong>'
                if '<span' in tag:
                    self.animation_text += '</span>'
                if '<em' in tag:
                    self.animation_text += '</em>'

            self.current_part_index += 1
            self.current_char_index = 0

        self.deep_searching_label.setText(self.animation_text)

    def fetch_and_process_best_result(self, search_input: Union[str, List[str]]) -> None:
        """Process search input and fetch results, with improved handling for deep research mode."""
        base_search_query = search_input[0] if isinstance(search_input, list) else search_input
        logger.debug(f"Fetching search results for: {search_input}")

        try:
            if hasattr(self, 'chat_reset_flag') and self.chat_reset_flag:
                logger.info("Search operation canceled due to chat reset")
                self.communicator.deep_searching_label_updated.emit("<b><font color='orange'> >>> Search canceled due to chat reset </font></b>")
                return

            last_user_input = self._get_last_user_input()
            is_deep_research = self.preferences.get("deep_research_enabled", False)
            num_results_per_query = 3 if is_deep_research else 5

            self.ai_generated_queries = None

            self._setup_search_ui_feedback(is_deep_research)

            if is_deep_research:
                self._update_search_label("Phase 1: Initial search with exact query...", "cyan")

                initial_search_queries = self._prepare_search_queries(search_input, is_deep_research, is_initial_search=True)
                if not initial_search_queries:
                    return

                initial_results = self._process_search_queries(initial_search_queries, num_results_per_query=2, is_initial_search=True)

                if not initial_results:
                    logger.warning("Initial search returned no results, falling back to standard approach")
                    search_queries = self._prepare_search_queries(search_input, is_deep_research, is_initial_search=False)
                    main_results = self._process_search_queries(search_queries, num_results_per_query)
                else:
                    self._update_search_label("Phase 2: Analyzing initial results...", "cyan")

                    self.ai_generated_queries = self._analyze_initial_results_and_generate_queries(
                        initial_results, base_search_query, last_user_input
                    )

                    follow_up_queries = self._prepare_search_queries(search_input, is_deep_research, is_initial_search=False)
                    if not follow_up_queries:
                        main_results = initial_results
                    else:
                        self._update_search_label("Phase 3: Performing targeted searches...", "cyan")
                        follow_up_results = self._process_search_queries(follow_up_queries, num_results_per_query)

                        main_results = initial_results + follow_up_results
            else:
                search_queries = self._prepare_search_queries(search_input, is_deep_research)
                if not search_queries:
                    return

                main_results = self._process_search_queries(search_queries, num_results_per_query)

            completion_message = "Deep Research Complete!" if is_deep_research else "Search Complete!"
            thread = threading.Thread(target=self.show_completion_message, args=(completion_message,))
            thread.start()

            if not main_results:
                self.communicator.response_received.emit(
                    "No relevant text extracted from the search results within token limit.<br><br>"
                )
                return

            search_result_text, best_result_urls = self._prepare_results_for_display(main_results)
            context_prefix = self._generate_context_prefix(last_user_input)

            search_instruction = (
                f"{context_prefix}\n\n"
                f"INSTRUCTION: Analyze the provided search results and provide a direct answer to the user's query or question. Do not perform additional searches.\n\n"
                f"SEARCH QUERY: \"{base_search_query}\"\n"
                f"SEARCH RESULTS:\n\n{search_result_text}\n\n"
                f"RESPONSE GUIDELINES:\n"
                f"1. Focus on directly answering the user's query or question based on the search results.\n"
                f"2. If the search results contain information relevant to the query, organize your response as follows:\n"
                f"   - Start with a direct answer to the user's question\n"
                f"   - Follow with supporting details from the search results\n"
                f"   - Use paragraphs for explanations rather than excessive bullet points\n"
                f"3. Maintain a conversational tone while being direct and informative.\n"
            )

            self.generate_response_thread(
                search_instruction, [best_result_urls], None, None,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )

        except Exception as e:
            logger.error(f"Error during fetching and processing: {str(e)}\n{traceback.format_exc()}")
            self.communicator.response_received.emit(
                "An error occurred during the search process. Please try again later.<br><br>" # Generic error for user
            )
            self.communicator.deep_searching_label_updated.emit("<b><font color='red'> >>> Search Error! </font></b>")

    def _get_last_user_input(self):
        try:
            current_input = self.input_Box.toPlainText().strip()
            if current_input:
                logger.info(f"Using current input box text as context: {current_input}")
                return current_input

            loaded_history = load_conversation_history(limit=5)  # Increased to get more context

            for role, message, _ in reversed(loaded_history):
                if role == "User":
                    if not message.lower().startswith("/search") and not message.lower().startswith("search for"):
                        logger.info(f"Using history message as context: {message}")
                        return message

            return ""
        except sqlite3.OperationalError as db_error:
            logger.error(f"Database access error: {str(db_error)}")
            self.communicator.response_received.emit(
                "<b><font color='orange'> >>> Warning: Unable to access conversation history due to database error</font></b><br><br>"
            )
            return ""

    def _update_search_label(self, message, color="green"):
        """Unified method to update the search label with proper formatting."""
        formatted_message = f"<b><font color='{color}'> >>> {message}</font></b>"
        self.communicator.deep_searching_label_updated.emit(formatted_message)
        self.show_deep_searching_label()  # Show the label above input box

    def _setup_search_ui_feedback(self, is_deep_research):
        """Initialize UI feedback for search operations."""
        message = "Deep Research Initiated..." if is_deep_research else "Performing Research..."
        self._update_search_label(message)

    def _process_search_queries(self, search_queries, num_results_per_query, is_initial_search=False):
        global _genai_client # Access the global client
        if _genai_client is None:
            logger.error("Google Gen AI client is not initialized. Cannot perform token counting.")
            return []

        main_results = []
        session = requests.Session()
        total_tokens_from_sources = 0
        max_total_tokens = 100000

        model_name = "gemini-2.0-flash" # Define model_name here as string

        if is_initial_search and self.preferences.get("deep_research_enabled", False):
            num_results_per_query = 2
            logger.info(f"Initial deep research search: limiting to {num_results_per_query} sources per query")

        # Process each search query
        for query in search_queries:
            self._update_search_progress(query)
            search_results = list(search(query))[:num_results_per_query]
            if not search_results:
                self._handle_empty_search_results(query)
                continue

            for url in search_results:
                if total_tokens_from_sources >= max_total_tokens:
                    logger.info(f"Total token limit of {max_total_tokens} tokens reached. Stopping search.")
                    self.communicator.response_received.emit(
                        f"<b><font color='orange'> >>> Token limit of 100K reached for search results. Further sources skipped.</font></b>"
                    )
                    return main_results

                # Pass model_name string instead of the model object
                result = self._process_search_url(url, query, session, model_name, max_total_tokens - total_tokens_from_sources)
                if not result:
                    continue

                main_results.append(result)
                total_tokens_from_sources += result.get('token_count', 0)
                logger.info(f"Accumulated tokens from sources: {total_tokens_from_sources}/{max_total_tokens}")

                # Check if we need to delete sources to stay within token limit
                if total_tokens_from_sources > max_total_tokens * 0.9:  # If we're at 90% of the limit
                    main_results = self._delete_sources_to_fit_token_limit(main_results, max_total_tokens)
                    # Recalculate total tokens after deletion
                    total_tokens_from_sources = sum(result.get('token_count', 0) for result in main_results)
                    logger.info(f"After deletion: Accumulated tokens from sources: {total_tokens_from_sources}/{max_total_tokens}")

        return main_results

    def _analyze_initial_results_and_generate_queries(self, initial_results, original_query, user_input):
        """Analyze initial search results and generate better follow-up search queries."""
        global _genai_client # Access the global client
        if _genai_client is None:
            logger.error("Google Gen AI client is not initialized. Cannot generate follow-up queries.")
            return None

        if not initial_results or len(initial_results) == 0:
            logger.warning("No initial results to analyze for query generation")
            return None

        try:
            # Extract text from initial results
            combined_text = "\n\n".join(
                f"Source {i+1}: {result['text']}" for i, result in enumerate(initial_results)
            )

            # Create a prompt for the AI to analyze the content and generate better search queries
            analysis_prompt = f"""
            TASK: Analyze the provided search results and generate 3-4 more specific search queries that will help directly answer the user's question.

            USER QUERY: "{original_query}"
            USER CONTEXT: "{user_input}"

            SEARCH RESULTS:
            {combined_text[:10000]}  # Limit text to avoid token limits

            INSTRUCTIONS:
            1. Identify what specific information would directly answer the user's question
            2. Determine what key information is missing from the initial search results
            3. Generate 3-4 specific search queries that would find information to directly answer the user's question
            4. Each query should focus on finding precise information needed to provide a complete answer
            5. Format your response as a JSON array of strings containing ONLY the search queries
            6. DO NOT include any explanations, just the JSON array

            EXAMPLE OUTPUT FORMAT:
            ["query 1", "query 2", "query 3", "query 4"]
            """

            # Update UI to show analysis is in progress
            self._update_search_label("Analyzing initial results to generate better search queries...", "cyan")

            # Use the model to generate better search queries with retry logic
            # Use the global _genai_client for content generation
            model_name = "gemini-2.0-flash"
            max_retries = 3
            retry_count = 0
            response_text = ""

            while retry_count < max_retries:
                try:
                    # Log that we're making the API call
                    logger.info(f"Making API call to Gemini for query analysis (attempt {retry_count + 1}/{max_retries})")
                    response = _genai_client.models.generate_content(
                        model=model_name,
                        contents=[analysis_prompt]
                    )
                    logger.info("API call for query analysis completed successfully")
                    response_text = response.text
                    break  # Success, exit the retry loop
                except Exception as api_error:
                    # Check if this is a timeout or cancellation error
                    error_str = str(api_error).lower()
                    if "499" in error_str or "cancel" in error_str or "timeout" in error_str or "deadline" in error_str:
                        logger.warning(f"API call for query analysis timed out or was cancelled: {api_error}")
                        if retry_count < max_retries - 1:
                            retry_count += 1
                            wait_time = 2 * retry_count  # Exponential backoff
                            logger.info(f"Retrying API call for query analysis (attempt {retry_count + 1}/{max_retries}) in {wait_time} seconds")
                            time.sleep(wait_time)  # Wait before retrying
                        else:
                            logger.error(f"Maximum retries reached for API call for query analysis: {api_error}")
                            return None  # Return None to use default variations
                    else:
                        # Re-raise other API errors
                        logger.error(f"Error in API call for query analysis: {api_error}")
                        return None  # Return None to use default variations

            # Extract the JSON array from the response
            try:
                # Find JSON array in the response
                json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    queries = json.loads(json_str)

                    # Validate and clean up queries
                    if isinstance(queries, list) and len(queries) > 0:
                        # Ensure we have at least the original query
                        if original_query not in queries:
                            queries.insert(0, original_query)
                        # Limit to 5 queries maximum
                        queries = queries[:5]
                        logger.info(f"Generated {len(queries)} search queries from initial results analysis")
                        return queries
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"Error parsing generated queries: {e}")

            # Fallback if JSON parsing failed
            logger.warning("Failed to parse AI-generated queries, extracting manually")
            # Try to extract queries line by line
            lines = response_text.strip().split('\n')
            queries = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('[') and not line.startswith(']') and not line.startswith('"') and len(line) > 5:
                    # Clean up the line
                    line = line.strip('"').strip("'").strip(',').strip()
                    if line:
                        queries.append(line)

            if queries:
                # Ensure we have at least the original query
                if original_query not in queries:
                    queries.insert(0, original_query)
                # Limit to 5 queries maximum
                queries = queries[:5]
                logger.info(f"Manually extracted {len(queries)} search queries from AI response")
                return queries

        except Exception as e:
            logger.error(f"Error analyzing initial results: {e}")

        # If all else fails, return None to use default variations
        return None

    def _update_search_progress(self, query):
        """Update UI with current search query."""
        self._update_search_label(f"Searching for: {query}", "White")

    def _handle_empty_search_results(self, query):
        """Handle case when no search results are found."""
        logger.warning(f"No search results found for query: {query}")
        self._update_search_label(f"No search results found for: {query}", "red")

    def _process_search_url(self, url, query, session, model, remaining_tokens):
        """Process a single search URL."""
        logger.debug(f"Fetching URL: {url}")
        try:
            return self.fetch_url(url, query, session, model, remaining_tokens)
        except Exception as e:
            logger.error(f"Error fetching URL: {url} - {str(e)}")
            return None

    def _handle_token_limit_exceeded(self, url):
        """Handle case when token limit is exceeded."""
        domain = self.extract_domain_name(url)
        self._update_search_label(f"Skipping source from URL: {domain} due to token limit.", "orange")

    def _delete_sources_to_fit_token_limit(self, results, max_tokens):
        """
        Delete sources that contribute least to the search results to stay within token limit.

        Args:
            results: List of search result dictionaries
            max_tokens: Maximum token limit to stay under

        Returns:
            Filtered list of results after removing less relevant sources
        """
        if not results:
            return results

        # Sort results by token count (descending)
        results_with_index = [(i, result) for i, result in enumerate(results)]
        results_with_index.sort(key=lambda x: x[1].get('token_count', 0), reverse=True)

        # Calculate current total tokens
        total_tokens = sum(result.get('token_count', 0) for result in results)
        logger.info(f"Current total tokens: {total_tokens}, max allowed: {max_tokens}")

        # If we're under the limit, no need to delete anything
        if total_tokens <= max_tokens:
            return results

        # Start removing the largest sources until we're under the limit
        deleted_sources = []
        remaining_results = list(results)  # Create a copy to avoid modifying during iteration

        # First try to remove sources with largest token counts
        for _, result in results_with_index:
            if total_tokens <= max_tokens * 0.9:  # Target 90% of max to leave some buffer
                break

            # Skip if this is the only result left
            if len(remaining_results) <= 1:
                break

            # Remove this result
            token_count = result.get('token_count', 0)
            domain = self.extract_domain_name(result.get('url', 'unknown'))
            remaining_results.remove(result)
            deleted_sources.append(domain)
            total_tokens -= token_count

            logger.info(f"Deleted source from {domain} with {token_count} tokens to stay within limit")

        # Notify user about deleted sources
        if deleted_sources:
            deleted_msg = f"<b><font color='orange'> >>> Deleted {len(deleted_sources)} sources to stay within token limit: {', '.join(deleted_sources)}</font></b>"
            self.communicator.response_received.emit(deleted_msg)
            self._update_search_label(f"Deleted {len(deleted_sources)} sources to stay within token limit", "orange")

        return remaining_results


    def _prepare_results_for_display(self, main_results):
        filtered_results = [(i, result) for i, result in enumerate(main_results) if result.get('text', '').strip()]

        # Build search result text with structured data highlighted
        search_result_parts = []
        for i, (_, result) in enumerate(filtered_results):
            source_text = f"Source {i + 1}: "

            # Add metadata if available
            if 'metadata' in result and result['metadata']:
                source_text += f"\nMetadata: {result['metadata']}"

            # Add the main text content
            source_text += f"\n{result['text']}"

            search_result_parts.append(source_text)

        search_result_text = "\n\n".join(search_result_parts)

        best_result_urls = "  ".join(
            f"<a href='{result['url']}' style='color: #61b0ee;'>{self.extract_domain_name(result['url'])}</a>"
            for result in main_results
        )
        # Add a note about content types if mixed
        content_types_present = {result.get('content_type', 'webpage') for result in main_results}
        if 'youtube_transcript' in content_types_present and 'webpage' in content_types_present:
            best_result_urls += " (Results include webpage content and video transcripts)"

        # Also emit to the response area for reference
        self.communicator.response_received.emit(
            f"<b><font color='green'> >>> Combined info from: {best_result_urls}</font></b>"
        )

        return search_result_text, best_result_urls

    def extract_domain_name(self, url: str) -> str:
        """Extract and format the domain name from a URL."""
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        if domain.startswith("www."):
            domain = domain[4:]

        # Return capitalized domain name for better display
        return domain.capitalize()

    def fetch_url(self, url: str, search_query: str, session: requests.Session,
                model_name: str, remaining_tokens: int) -> dict: # Changed model to model_name: str
        """Fetch and process URL content with improved error handling and token management."""
        global _genai_client # Access the global client
        if _genai_client is None:
            logger.error("Google Gen AI client is not initialized. Cannot count tokens for URL content.")
            return None

        max_tokens_per_source = min(100000, remaining_tokens)
        max_retries = 3
        retry_count = 0
        content_type_for_history = "Webpage" # Default

        while retry_count < max_retries:
            try:
                # Validate URL format
                if not url.startswith(('http://', 'https://')):
                    logger.warning(f"Invalid URL format: {url}")
                    return None
                
                parsed_url_for_type_check = urlparse(url)
                is_youtube_url = parsed_url_for_type_check.netloc in ["www.youtube.com", "youtube.com", "youtu.be"]
                
                page_text = None
                title = ""
                content_type_for_ai = "webpage"

                if is_youtube_url:
                    content_type_for_ai = "youtube_transcript"
                    content_type_for_history = "YouTube Transcript"
                    video_id = None
                    if parsed_url_for_type_check.netloc == "youtu.be":
                        video_id = parsed_url_for_type_check.path.lstrip("/")
                    elif parsed_url_for_type_check.path == "/watch":
                        query_params = parse_qs(parsed_url_for_type_check.query)
                        if 'v' in query_params:
                            video_id = query_params['v'][0]
                    elif parsed_url_for_type_check.path.startswith("/shorts/"):
                        video_id = parsed_url_for_type_check.path.split('/shorts/')[1]

                    if video_id:
                        self._update_search_label(f"Fetching transcript for YouTube ID: {video_id}...", "cyan")
                        try:
                            transcript_list = YouTubeTranscriptApi.get_transcript(video_id)
                            page_text = " ".join([item['text'] for item in transcript_list])
                            title = f"YouTube Video Transcript: {video_id}" # Basic title
                            if not page_text.strip():
                                logger.warning(f"Transcript found but is empty for YouTube video ID: {video_id} from URL: {url}")
                                page_text = None # Treat as no transcript
                            else:
                                logger.info(f"Transcript fetched successfully for YouTube video ID: {video_id} from URL: {url}")
                        except (TranscriptsDisabled, NoTranscriptFound) as specific_error:
                            msg = "Transcripts disabled" if isinstance(specific_error, TranscriptsDisabled) else "No transcript found"
                            logger.warning(f"{msg} for YouTube video ID: {video_id} from URL: {url}")
                            page_text = None # No transcript available
                        except Exception as e_yt:
                            logger.error(f"Error fetching transcript for YouTube video ID {video_id} (URL: {url}): {e_yt}")
                            page_text = None # Error fetching
                    else:
                        logger.warning(f"Could not extract YouTube video ID from URL: {url}. Will attempt webpage scrape.")
                        is_youtube_url = False # Fallback to webpage scrape for this "YouTube" URL

                if not is_youtube_url or page_text is None: # If not YouTube, or YouTube transcript failed/unavailable
                    content_type_for_ai = "webpage"
                    content_type_for_history = "Webpage"
                    # Fetch URL content
                    content = self._fetch_url_content(url, session)
                    if not content:
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.warning(f"Failed to fetch URL content (attempt {retry_count}/{max_retries}). Retrying...")
                            time.sleep(1)  # Wait before retrying
                            continue
                        else:
                            logger.error(f"Failed to fetch URL content after {max_retries} attempts: {url}")
                            return None

                    # Parse content with BeautifulSoup
                    try:
                        soup = BeautifulSoup(content, 'html.parser')
                    except Exception as soup_e:
                        logger.warning(f"BeautifulSoup parsing error for URL: {url} - {soup_e}. Skipping URL content.")
                        return None

                    # Extract title
                    title_element = soup.find('title')
                    if title_element:
                        title = title_element.text.strip()

                    # Extract main content
                    content_elements = soup.find_all(['p', 'h1', 'h2', 'li', 'blockquote'])
                    page_text = ' '.join(p.text for p in content_elements)

                    # If no content was found, try a more aggressive approach
                    if not page_text.strip():
                        logger.warning(f"No content found with standard selectors for URL: {url}. Trying alternative extraction.")
                        page_text = self._extract_all_text(soup)

                if not page_text.strip():
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"No content extracted from {url} (attempt {retry_count}/{max_retries}). Retrying...")
                        time.sleep(1)  # Wait before retrying
                        continue
                    else:
                        logger.error(f"No content extracted from {url} after {max_retries} attempts.")
                        return None

                if '\ufffd' in page_text:
                    logger.warning("Replacement characters detected in page content.")

                combined_text = page_text
                
                # Use the new client.models.count_tokens method
                source_token_count = _genai_client.models.count_tokens(model=model_name, contents=[combined_text]).total_tokens

                if source_token_count > max_tokens_per_source:
                    char_per_token = len(combined_text) / source_token_count if source_token_count > 0 else 4
                    safe_char_limit = int(max_tokens_per_source * char_per_token * 0.95)
                    combined_text = combined_text[:safe_char_limit] + "... [text truncated to stay within 100K token limit]"
                    logger.info(f"Text truncated from approximately {source_token_count} tokens to {max_tokens_per_source} tokens for URL '{url}'")
                    source_token_count = _genai_client.models.count_tokens(model=model_name, contents=[combined_text]).total_tokens
                
                if combined_text:
                    history_text = f"Search Result ({content_type_for_history}) For: {url}\nTitle: {title}\n"
                    history_text += f"{combined_text}\n"
                    save_conversation_history("System", history_text)

                    return {
                        'text': combined_text,
                        'url': url,
                        'title': title,
                        'token_count': source_token_count,
                        'content_type': content_type_for_ai # Add content type
                    }
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"Empty processed content for {url} (attempt {retry_count}/{max_retries}). Retrying...")
                        time.sleep(1)  # Wait before retrying
                        continue
                    else:
                        logger.warning(f"Empty processed content for {url} after {max_retries} attempts.")
                        return None

            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"Error fetching URL {url} (attempt {retry_count}/{max_retries}): {e}. Retrying...")
                    time.sleep(1)  # Wait before retrying
                    continue
                else:
                    logger.warning(f"Error fetching URL {url} after {max_retries} attempts: {e}. Skipping URL.")
                    return None
            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    logger.error(f"Unexpected error processing URL {url} (attempt {retry_count}/{max_retries}): {e}", exc_info=True)
                    time.sleep(1)  # Wait before retrying
                    continue
                else:
                    logger.error(f"Unexpected error processing URL {url} after {max_retries} attempts: {e}", exc_info=True)
                    return None


# URL INPUT HANDLER ##################################################################################################################################################################################################
    def handle_url_input(self, url, user_input):
        """Process a URL input from the user by fetching its content or transcript."""
        try:
            time.sleep(1)
            self.communicator.response_received.emit(
                "<b><font color='cyan'>>> URL input detected. Checking for information...</font></b>"
            )

            extracted_text = None
            content_type = "webpage" # Default to webpage

            # Check if it's a YouTube URL
            parsed_url = urlparse(url)
            if parsed_url.netloc in ["www.youtube.com", "youtube.com", "youtu.be"]:
                content_type = "youtube_video"
                video_id = None
                if parsed_url.netloc == "youtu.be":
                    video_id = parsed_url.path.lstrip("/")
                elif parsed_url.path == "/watch":
                    query_params = parse_qs(parsed_url.query)
                    if 'v' in query_params:
                        video_id = query_params['v'][0]
                elif parsed_url.path.startswith("/shorts/"):
                    video_id = parsed_url.path.split('/shorts/')[1]
                    # video_id = query_params['v'][0] # This line was causing the error and is redundant

                extracted_text = None # Initialize before attempting to fetch
                if video_id:
                    self.communicator.response_received.emit(
                        f"<b><font color='cyan'>>> Fetching transcript for YouTube video ID: {video_id}...</font></b>"
                    )
                    MAX_RETRIES = 2  # Total 3 attempts (initial + 2 retries)
                    RETRY_DELAY_SECONDS = 2 # Base delay, will increase

                    for attempt in range(MAX_RETRIES + 1):
                        try:
                            transcript_list = YouTubeTranscriptApi.get_transcript(video_id)
                            extracted_text_candidate = " ".join([item['text'] for item in transcript_list])
                            if not extracted_text_candidate.strip():
                                self.communicator.response_received.emit(
                                    f"<b><font color='orange'>>> Transcript found but is empty for video ID: {video_id}.</font></b><br>"
                                )
                                extracted_text = None # Explicitly None for empty transcript
                            else:
                                self.communicator.response_received.emit(
                                    f"<b><font color='green'>>> Transcript fetched successfully for video ID: {video_id}.</font></b>"
                                )
                                extracted_text = extracted_text_candidate
                            break # Success or empty transcript, no need to retry
                        except (TranscriptsDisabled, NoTranscriptFound) as specific_error:
                            msg = "Transcripts are disabled" if isinstance(specific_error, TranscriptsDisabled) else "No transcript found"
                            self.communicator.response_received.emit(
                                f"<b><font color='orange'>>> {msg} for video ID: {video_id}.</font></b><br>"
                            )
                            extracted_text = None
                            break # Definitive, no need to retry
                        except Exception as e_yt: # Catches other errors like the XML parsing one
                            logger.error(f"Attempt {attempt + 1}/{MAX_RETRIES + 1} to fetch transcript for {video_id} failed: {e_yt}")
                            if attempt < MAX_RETRIES:
                                current_delay = RETRY_DELAY_SECONDS * (attempt + 1)
                                self.communicator.response_received.emit(
                                    f"<b><font color='orange'>>> Attempt {attempt + 1} failed for transcript {video_id}. Retrying in {current_delay}s...</font></b><br>"
                                )
                                time.sleep(current_delay) # Increasing delay
                            else: # Last attempt failed
                                self.communicator.response_received.emit(
                                    f"<b><font color='red'>>> Error fetching transcript for video ID {video_id} after {MAX_RETRIES + 1} attempts: {str(e_yt)}</font></b><br><br>"
                                )
                                extracted_text = None # Ensure it's None after all retries fail
                                # No break here, loop will end, extracted_text remains None

                    # After the loop, extracted_text will either have the transcript,
                    # or be None if fetching failed or no transcript was available.

                else:
                    self.communicator.response_received.emit(
                        f"<b><font color='red'>>> Could not extract YouTube video ID from URL: {url}</font></b><br><br>"
                    )
            
            if extracted_text is None and content_type == "webpage":
                main_page_content = self._fetch_url_content(url)
                if not main_page_content:
                    self.communicator.response_received.emit(
                        f"Error: Unable to fetch content from {url}.<br><br>"
                    )
                    return
                try:
                    soup = BeautifulSoup(main_page_content, 'html.parser')
                    content_elements = soup.find_all(['p', 'h1', 'h2', 'li', 'blockquote', 'div', 'article'])
                    extracted_text = ' '.join(element.get_text(separator=' ', strip=True) for element in content_elements)
                    if not extracted_text.strip() or len(extracted_text.strip()) < 200:
                        extracted_text = self._extract_all_text(soup)
                except Exception as soup_e:
                    self.communicator.response_received.emit(f"Error: Unable to parse content from {url}: {str(soup_e)}.<br><br>")
                    return
            
            if not extracted_text or not extracted_text.strip():
                self.communicator.response_received.emit(f"Could not retrieve meaningful content from URL: {url}.<br><br>")
                return

            url_pattern_re = re.compile(r'(https?://\S+)')
            url_match = url_pattern_re.search(user_input.strip())
            user_instructions = ""
            if url_match:
                url_end_index = url_match.end()
                if url_end_index < len(user_input):
                    user_instructions = user_input[url_end_index:].strip()

            if content_type == "youtube_video":
                combined_text = f"The following is a transcript from the YouTube video at {url}\n\n"
                if user_instructions:
                    combined_text += f"User Instructions for Video Analysis: {user_instructions}\n\n"
                else:
                    combined_text += """User Instructions for Video Analysis:
Based on the following video transcript, provide a comprehensive and in-depth analysis. Focus on extracting the most important information and insights a user would likely want to know. Please structure your analysis to cover the following aspects:

1.  **Overall Summary & Purpose:** Briefly summarize the entire video and state its primary purpose (e.g., educational, tutorial, review, news, entertainment, persuasive).
2.  **Key Themes & Main Topics:** Identify the central themes and main topics discussed throughout the video.
3.  **Core Arguments/Messages:** What are the most significant arguments, messages, or points the video is trying to convey?
4.  **Detailed Breakdown & Important Information:**
    *   Extract and detail the most crucial pieces of information, facts, data, or specific details presented.
    *   If it's a tutorial or instructional video, list the key steps or techniques demonstrated.
    *   If it's a discussion or interview, highlight the main questions asked and the core responses or viewpoints shared.
5.  **Key Takeaways/Conclusions:** What are the main conclusions reached or the most important takeaways for the viewer?
6.  **Speaker(s) & Perspective (if discernible):** If identifiable from the transcript, who is speaking and what is their apparent role, expertise, or perspective?
7.  **Noteworthy Segments/Quotes:** Are there any particularly impactful, insightful, or memorable segments or direct quotes from the transcript?
8.  **Target Audience (if inferable):** Who do you think this video is primarily intended for?

Provide a clear, well-organized, and descriptive analysis.
"""
                combined_text += f"Video Transcript Content:\n{extracted_text}"
            else: # Webpage
                combined_text = f"The following is content extracted from the webpage at {url}\n\n"
                if user_instructions:
                    combined_text += f"User Instructions for Webpage Analysis: {user_instructions}\n\n"
                combined_text += f"Extracted Webpage Content:\n{extracted_text}"

            max_chars_for_ai = 150000 # Increased limit slightly, but token counting is better
            if len(combined_text) > max_chars_for_ai:
                combined_text = combined_text[:max_chars_for_ai] + "\n\n[...Content truncated due to length...]"
                logger.info(f"Combined text for AI truncated to {max_chars_for_ai} characters.")

            save_conversation_history("System", f"Content from URL ({content_type}): {url}. Instructions: '{user_instructions}'. First 200 chars of content: {extracted_text[:200]}")
            # Save the full transcript to history if it's a YouTube video and text was extracted
            if content_type == "youtube_video" and extracted_text and extracted_text.strip():
                save_conversation_history("Document", f"Full Transcript from YouTube Video ({url}):\n\n{extracted_text}")

            self.generate_response_thread(combined_text, None, None, None, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        except Exception as e:
            self.communicator.response_received.emit(f"Error processing URL: {str(e)}<br><br>")
            logger.error(f"Error in handle_url_input: {e}", exc_info=True)

    def _fetch_url_content(self, url, session=None):
        """Unified method to fetch URL content with proper error handling (no retry logic)."""
        try:
            # Use random user agent to avoid rate limiting
            headers = {
                "User-Agent": get_random_user_agent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            }

            # Log the user agent being used
            logger.debug(f"Using User-Agent: {headers['User-Agent']} for URL: {url}")

            # Use provided session or create a new one
            if session:
                response = session.get(url, headers=headers, timeout=10)
            else:
                response = requests.get(url, headers=headers, timeout=10)

            response.raise_for_status()
            response.encoding = 'utf-8'

            if not response.text:
                logger.warning(f"Empty response content for URL: {url}")
                return None

            return response.text

        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching URL: {url} - {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching URL: {url} - {str(e)}")
            return None


    def _extract_all_text(self, soup):
        """Extract all text from the page as a fallback method."""
        # Remove script and style elements that contain non-relevant text
        for script_or_style in soup(['script', 'style', 'noscript', 'iframe', 'head']):
            script_or_style.extract()

        # Get all text
        text = soup.get_text(separator=' ', strip=True)

        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Estimate tokens (approximately 4 characters per token)
        estimated_tokens = len(text) // 4

        # If estimated tokens exceed our limit, truncate the text
        max_tokens = 100000
        if estimated_tokens > max_tokens:
            # Calculate character to token ratio (approx 4 chars per token)
            char_per_token = 4
            # Truncate to slightly less than max_tokens to be safe
            safe_char_limit = int(max_tokens * char_per_token * 0.95)
            text = text[:safe_char_limit] + "... [text truncated to stay within 100K token limit]"
            logger.info(f"Text truncated from approximately {estimated_tokens} tokens to {max_tokens} tokens")

        return text

    def fetch_with_requests(self, url):
        """Legacy method maintained for compatibility, uses the unified _fetch_url_content method."""
        return self._fetch_url_content(url)


# DOCUMENT UPLOADING AND PROCESSING ##################################################################################################################################################################################################
    def keyPressEvent(self, event):
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            if getattr(self, 'waiting_for_document_context', False):
                self.waiting_for_document_context = False
                # Get user input to use as instructions for document processing
                user_input = self.input_Box.toPlainText().strip()

                # Process the document with either custom or default instructions
                if user_input:
                    # Process with custom instructions
                    self._append_user_input(user_input)
                    self.process_uploaded_document(user_context=user_input)
                else:
                    # Process with default instructions when input is empty
                    logger.info("Processing document with default instructions")
                    self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> Processing with default analysis</font></b>")
                    self.process_uploaded_document(user_context='')

                # Clear input box and reset height
                self.input_Box.clear()
                self.input_Box.setFixedHeight(self.input_Box.min_height)
                return

            # Movie animation code removed

    def upload_document(self):
        file_filters = [
            "All files (*.*)",
            "Documents (*.pdf *.docx *.doc *.pptx *.ppt *.txt *.json *.py *.bat)",
            "Spreadsheets (*.xlsx *.xls *.csv *.parquet)"
        ]
        options = QFileDialog.Options() | QFileDialog.DontUseNativeDialog
        file_dialog = QFileDialog()
        file_dialog.setWindowTitle("Upload Document")
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setViewMode(QFileDialog.Detail)
        file_dialog.setNameFilters(file_filters)

        if file_dialog.exec_():
            file_paths = [url.toLocalFile() for url in file_dialog.selectedUrls()]

            if len(file_paths) > 1:
                log_message = f"{len(file_paths)} Documents Attached"
                logger.info(log_message)
                # Show message in deep_searching_label instead of deep_thinking_log
                self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}</font></b><br><font color='white'>Enter your instructions for document analysis or press Enter to use default analysis</font>")
                self.show_deep_searching_label()
                self.waiting_for_document_context = True
                self.activateWindow()
                self.raise_()
                # Save and update placeholder text to guide the user
                self.original_placeholder = self.input_Box.placeholderText()
                self.input_Box.setPlaceholderText("Enter instructions for document analysis (e.g., 'Summarize key points' or 'Extract financial data')")
                self.current_upload_files = file_paths
            else:
                file_path = file_paths[0]
                original_file_name = os.path.basename(file_path)
                file_extension = os.path.splitext(file_path)[1].lower()
                self.current_upload_file_path = file_path
                self.current_upload_file_name = original_file_name
                self.current_upload_file_extension = file_extension

                log_message = "Document Uploaded"
                logger.info(log_message)
                # Show message in deep_searching_label instead of deep_thinking_log
                self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> {log_message}: {original_file_name}</font></b><br><font color='white'>Enter your instructions for document analysis or press Enter to use default analysis</font>")
                self.show_deep_searching_label()
                self.waiting_for_document_context = True
                self.activateWindow()
                self.raise_()
                # Save and update placeholder text to guide the user
                self.original_placeholder = self.input_Box.placeholderText()
                self.input_Box.setPlaceholderText("Enter instructions for document analysis (e.g., 'Summarize key points' or 'Extract financial data')")

    def update_placeholder_text(self):
        """Update the placeholder text in the input box using the greeting placeholder."""
        try:
            new_placeholder = self.get_greeting_placeholder()
            self.input_Box.setPlaceholderText(new_placeholder)
        except Exception as e:
            logger.error(f"Error updating placeholder text: {e}", exc_info=True)

    def process_uploaded_document(self, user_context=''):
        try:
            if hasattr(self, 'current_upload_files') and self.current_upload_files:
                file_paths = self.current_upload_files
            elif hasattr(self, 'current_upload_file_path') and self.current_upload_file_path:
                file_paths = [self.current_upload_file_path]
            else:
                raise ValueError("No files selected for processing or waiting for user input on image")

            if not file_paths or not all(isinstance(path, str) for path in file_paths):
                raise ValueError("Invalid file paths provided")

            thread = threading.Thread(target=self._process_files_threaded, args=(file_paths, user_context), daemon=True)
            thread.start()

        except Exception as e:
            self.communicator.response_received.emit(f"Error: Could not process file. {str(e)}<br><br>")
            logger.error(f"Error in process_uploaded_document: {e}", exc_info=True)
            # Show error message in deep_searching_label
            self.communicator.deep_searching_label_updated.emit("<b><font color='red'> >>> Document Processing Error! </font></b>")
            thread = threading.Thread(target=lambda: (time.sleep(1), self.deep_searching_label.hide()))
            thread.start()
        finally:
            # Reset file attributes in one line
            self.current_upload_file_path = self.current_upload_file_name = self.current_upload_file_extension = self.current_upload_files = None
            # Restore original placeholder text
            self.update_placeholder_text()

    def _process_files_threaded(self, file_paths, user_context):
        try:
            # Filter out image, audio, and video files
            excluded_extensions = set(self.image_extensions + self.audio_extensions + self.video_extensions)
            valid_files = [path for path in file_paths if os.path.exists(path) and
                          os.path.splitext(path)[1].lower() not in excluded_extensions]

            if not valid_files:
                self.communicator.response_received.emit("No valid document files to process. Image, audio, and video files are not supported for text analysis.<br><br>")
                return

            with concurrent.futures.ThreadPoolExecutor(max_workers=min(5, len(valid_files))) as executor:
                self._process_non_image_files(valid_files, user_context, executor, valid_files)

        except Exception as e:
            self.communicator.response_received.emit(f"Error processing files: {str(e)}<br><br>")
            logger.error(f"Error in _process_files_threaded: {e}", exc_info=True)
            # Show error message in deep_searching_label
            self.communicator.deep_searching_label_updated.emit("<b><font color='red'> >>> Document Processing Error! </font></b>")
            # We'll let the _update_deep_searching_label_slot method handle the timer
            # since it will detect this is an error message

    def _process_non_image_files(self, file_paths, user_context, executor, all_files):
        futures_to_paths = {}

        # Display a single message for all documents instead of per-document messages
        self.communicator.deep_searching_label_updated.emit(f"<b><font color='white'> >>> Analysing All Documents</font></b>")

        # Start all processing tasks
        for file_path in file_paths:
            futures_to_paths[executor.submit(self._process_single_non_image_file, file_path, user_context)] = file_path

        # Collect results efficiently
        combined_data = []
        for future in concurrent.futures.as_completed(futures_to_paths):
            try:
                file_data = future.result()
                if file_data:
                   combined_data.append(file_data)
            except Exception as e:
                file_path = futures_to_paths[future]
                logger.error(f"Error processing file {os.path.basename(file_path)}: {e}", exc_info=True)

        if combined_data:
            full_combined_data = "\n".join(combined_data)

            # Construct the prompt with user instructions if provided
            if user_context and user_context.strip():
                # Include user's specific instructions in the prompt
                logger.info(f"Including user instructions in document analysis: {user_context}")
                preprompt_message = f"Analyse the following document(s) according to these user instructions:\n\n"
                preprompt_message += f"USER INSTRUCTIONS: {user_context}\n\n"
                preprompt_message += f"IMPORTANT: Focus on the document's content details and summary rather than metadata. Strictly follow the user's specific instructions above all else.\n\n"
                preprompt_message += f"If no specific analysis structure is requested, use this default structure:\n"
                preprompt_message += f"1. Document Overview: A brief introduction to what the document is about.\n"
                preprompt_message += f"2. Key Highlights: Present important points from the document as bullet points to emphasize critical information.\n"
                preprompt_message += f"3. Detailed Content Analysis: Analyze the main sections and important details of the document's content.\n"
                preprompt_message += f"4. Executive Summary: Create a comprehensive summary that captures the main themes, findings, and conclusions of the document.\n\n"
            else:
                # Default prompt without user instructions
                preprompt_message = f"Analyse the following document(s) and provide a structured analysis focusing on the document's content details and summary rather than metadata:\n\n"
                preprompt_message += f"1. Document Overview: A brief introduction to what the document is about.\n\n"
                preprompt_message += f"2. Key Highlights: Present important points from the document as bullet points to emphasize critical information.\n\n"
                preprompt_message += f"3. Detailed Content Analysis: Analyze the main sections and important details of the document's content.\n\n"
                preprompt_message += f"4. Executive Summary: Create a comprehensive summary that captures the main themes, findings, and conclusions of the document.\n\n"

            full_combined_data_with_prompt = preprompt_message + full_combined_data
            thread = threading.Thread(target=self.generate_response_thread,
                                     args=(full_combined_data_with_prompt, None, "Multiple files processed",
                                           None, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            thread.start()
            # Show document analysis complete message before hiding
            self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FF00'> >>> Document Analysis Complete! </font></b>")
            # We'll let the _update_deep_searching_label_slot method handle the timer
            # since it will detect this is a completion message

    def _process_single_non_image_file(self, file_path, user_context):
        try:
            file_name = os.path.basename(file_path)
            file_data = self._read_file_content(file_path)
            if file_data:
                save_conversation_history("Document", f"File: {file_name}\n{file_data}")
                return file_data
            else:
                self.communicator.response_received.emit(f"No valid data to process from file: {file_name}<br><br>")
                return None
        except Exception as e:
            self.communicator.response_received.emit(f"Error processing non-image file: {os.path.basename(file_path)} - {str(e)}<br><br>")
            logger.error(f"Error in _process_non_image_files: {e}", exc_info=True)
            return None

    def _read_file_content(self, file_path: str) -> str:
        """
        Reads the content of a file using the appropriate handler based on its extension.
        For unrecognized file types, attempts to read as a text file.

        :param file_path: The path to the file.
        :return: The content of the file as a string, or None if reading fails.
        """
        file_extension = os.path.splitext(file_path)[1].lower()
        file_name = os.path.basename(file_path)

        # Check if file is an image, audio, or video file
        excluded_extensions = set(self.image_extensions + self.audio_extensions + self.video_extensions)
        if file_extension in excluded_extensions:
            self.communicator.response_received.emit(f"File type not supported for text analysis: {file_name}. Image, audio, and video files are excluded.<br><br>")
            return None

        file_handlers = {
            ".pdf": self.read_pdf,
            ".xlsx": self.read_excel_file,
            ".xls": self.read_excel_file,
            ".txt": self.read_text_file,
            ".json": self.read_json_file,
            ".bat": self.read_text_file,
            ".docx": self.read_word_document,
            ".doc": self.read_doc_as_text,
            ".pptx": self.read_powerpoint_document,
            ".ppt": self.read_powerpoint_document,
            ".csv": self.read_csv_file,
            ".parquet": self.read_parquet_file,
        }

        if file_extension in file_handlers:
            try:
                data = file_handlers[file_extension](file_path)
                if data is not None:
                    return str(data).strip()
            except Exception as e:
                self.communicator.response_received.emit(f"Error reading file: {file_name} - {str(e)}<br><br>")
                logger.error(f"Error in _read_file_content: Could not read file {file_path}: {e}", exc_info=True)
        else:
            # Try to read unrecognized file types as text files
            logger.info(f"Attempting to read unrecognized file type {file_extension} as text: {file_path}")
            try:
                data = self.read_text_file(file_path)
                if data is not None:
                    logger.info(f"Successfully read {file_extension} file as text: {file_path}")
                    return f"[File read as plain text - original format: {file_extension}]\n\n{str(data).strip()}"
                else:
                    # Try binary reading as a fallback
                    try:
                        with open(file_path, 'rb') as binary_file:
                            binary_data = binary_file.read(1024 * 1024)  # Read up to 1MB
                            # Extract readable text from binary content
                            text_content = ""
                            for i in range(0, len(binary_data)):
                                if binary_data[i] > 31 and binary_data[i] < 127:  # ASCII printable characters
                                    text_content += chr(binary_data[i])

                            if len(text_content) > 100:  # Arbitrary threshold for meaningful content
                                logger.info(f"Successfully extracted text from binary file: {file_path}")
                                return f"[File read as binary with text extraction - original format: {file_extension}]\n\n{text_content}"
                            else:
                                self.communicator.response_received.emit(f"Could not extract meaningful text from {file_extension} file.<br><br>")
                    except Exception as binary_e:
                        logger.error(f"Error reading file as binary: {file_path}: {binary_e}", exc_info=True)
                        self.communicator.response_received.emit(f"Could not read {file_extension} file as binary.<br><br>")
            except Exception as e:
                self.communicator.response_received.emit(f"Error reading file as text: {file_name} - {str(e)}<br><br>")
                logger.error(f"Error reading unrecognized file type as text {file_path}: {e}", exc_info=True)
        return None

    def read_text_file(self, file_path):
        """Read a text file with optimal encoding handling."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace', buffering=1024*1024) as file:
                return file.read()
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read text file. {str(e)}<br><br>")
            return None

    def read_doc_as_text(self, file_path):
        """Read .doc file by converting it to text format."""
        try:
            # Create a temporary file path for the text output
            temp_txt_path = file_path + ".txt"

            # Log the process
            logger.info(f"Converting .doc file to text: {file_path}")

            # Try to extract text directly first
            try:
                # Try to open as binary and extract text
                with open(file_path, 'rb') as doc_file:
                    content = doc_file.read()
                    # Look for text content in the binary file
                    text_content = ""
                    # Extract readable text from binary content
                    for i in range(0, len(content)):
                        if content[i] > 31 and content[i] < 127:  # ASCII printable characters
                            text_content += chr(content[i])

                # If we got reasonable text content, use it
                if len(text_content) > 100:  # Arbitrary threshold to ensure we got meaningful content
                    logger.info(f"Successfully extracted text from .doc file: {file_path}")
                    return "Document Content (extracted from .doc file):\n\n" + text_content
            except Exception as extract_e:
                logger.warning(f"Direct text extraction failed, will try alternative method: {extract_e}")

            # If direct extraction failed, try to use external tools if available
            try:
                import subprocess
                # Try to use catdoc if available (common on Linux/macOS)
                result = subprocess.run(['catdoc', file_path], capture_output=True, text=True, check=False)
                if result.returncode == 0 and result.stdout:
                    logger.info(f"Successfully converted .doc file using catdoc: {file_path}")
                    return "Document Content (converted from .doc file):\n\n" + result.stdout
            except Exception as tool_e:
                logger.warning(f"External tool conversion failed: {tool_e}")

            # As a last resort, read the file as text with error handling
            logger.info(f"Falling back to basic text extraction for .doc file: {file_path}")
            return self.read_text_file(file_path)

        except Exception as e:
            logger.error(f"Error processing .doc file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not process .doc file. {str(e)}<br><br>")
            return "Could not fully process the .doc file. Only partial content may be available.\n\nPlease consider converting the document to .docx or .txt format for better results."


    def _perform_ocr(self, image):
        """
        Perform enhanced OCR on an image with multiple preprocessing techniques and fallback methods.
        """
        try:
            # Get the correct tessdata directory path
            tessdata_dir = os.path.abspath(os.path.join(veritas_docs_dir, 'Tesseract-OCR', 'tessdata'))
            # Set environment variable directly
            os.environ['TESSDATA_PREFIX'] = tessdata_dir
            # Create config without quotes in the path
            tessdata_dir_config = f'--tessdata-dir {tessdata_dir}'

            results = []
            try:
                gray_image = image if image.mode == 'L' else image.convert('L')
                try:
                    img_array = np.array(gray_image)
                    thresh_array = (img_array > 150) * 255
                    thresh_image = Image.fromarray(thresh_array.astype('uint8'))
                except ImportError:
                    threshold_table = [0 if i < 150 else 255 for i in range(256)]
                    thresh_image = gray_image.point(threshold_table, mode='1')

                try:
                    # Use direct pytesseract call without config first
                    text1 = pytesseract.image_to_string(thresh_image, lang='eng', timeout=30)
                    if text1.strip():
                        results.append(text1.strip())
                        logger.info("Standard OCR attempt succeeded")
                except Exception as e:
                    logger.warning(f"Standard OCR attempt failed: {e}")

                try:
                    # Use simple config without quotes
                    config = '-l eng --psm 6'
                    text2 = pytesseract.image_to_string(thresh_image, config=config, timeout=30)
                    if text2.strip() and text2.strip() not in results:
                        results.append(text2.strip())
                        logger.info("OCR with PSM 6 succeeded")
                except Exception as e:
                    logger.warning(f"OCR with PSM 6 failed: {e}")
            except Exception as preprocess1_e:
                logger.warning(f"First preprocessing technique failed: {preprocess1_e}")

            try:
                gray_image = image.convert('L')

                enhancer = ImageEnhance.Contrast(gray_image)
                enhanced_image = enhancer.enhance(2.0)  # Increase contrast significantly

                # Use simple config without tessdata path
                config = '-l eng --psm 4'  # Assume single column of text
                text3 = pytesseract.image_to_string(enhanced_image, config=config, timeout=30)
                if text3.strip() and text3.strip() not in results:
                    results.append(text3.strip())
                    logger.info("OCR with contrast enhancement succeeded")
            except Exception as preprocess2_e:
                logger.warning(f"Second preprocessing technique failed: {preprocess2_e}")

            try:
                if image.width < 1000 or image.height < 1000:
                    scale_factor = max(1000 / image.width, 1000 / image.height)
                    new_size = (int(image.width * scale_factor), int(image.height * scale_factor))
                    resized_image = image.resize(new_size, Image.LANCZOS)
                    # Use simple config without tessdata path
                    config = '-l eng --psm 3'  # Fully automatic page segmentation
                    text4 = pytesseract.image_to_string(resized_image, config=config, timeout=30)
                    if text4.strip() and text4.strip() not in results:
                        results.append(text4.strip())
                        logger.info("OCR with image resizing succeeded")
            except Exception as preprocess3_e:
                logger.warning(f"Third preprocessing technique failed: {preprocess3_e}")

            try:
                if not results:
                    # Try legacy engine as last resort
                    legacy_config = '-l eng --oem 0 --psm 6'
                    text5 = pytesseract.image_to_string(image, config=legacy_config, timeout=30)
                    if text5.strip():
                        results.append(text5.strip())
                        logger.info("OCR with legacy engine succeeded")
            except Exception as legacy_e:
                logger.warning(f"Legacy engine OCR attempt failed: {legacy_e}")

            # Try one more approach if all else fails
            try:
                if not results:
                    # Try with alternative config
                    alt_config = '-l eng --psm 1'
                    text6 = pytesseract.image_to_string(image, config=alt_config, timeout=30)
                    if text6.strip():
                        results.append(text6.strip())
                        logger.info("OCR with alternative config succeeded")
            except Exception as alt_e:
                logger.warning(f"Alternative OCR attempt failed: {alt_e}")

            if results:
                results.sort(key=len, reverse=True)
                return results[0]
            else:
                return ""
        except Exception as e:
            logger.error(f"Error during enhanced OCR: {e}", exc_info=True)
            return ""

    def read_text_file(self, file_path):
        """Read a text file with optimal encoding handling."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace', buffering=1024*1024) as file:
                return file.read()
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read text file. {str(e)}<br><br>")
            return None

    def read_json_file(self, file_path):
        """Read and format JSON file with optimized structure preservation for LLM processing."""
        try:
            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

            # Initialize output sections
            output_sections = []

            # Add file metadata header
            output_sections.append(f"=== JSON FILE: {file_name} ===")
            output_sections.append(f"File Size: {file_size:.2f} MB")

            # Read the JSON data
            with open(file_path, 'r', encoding='utf-8', errors='replace', buffering=1024*1024) as json_file:
                data = json.load(json_file)

            # Analyze JSON structure
            structure_section = ["=== JSON STRUCTURE ==="]

            # Determine if it's an array or object
            if isinstance(data, list):
                structure_section.append(f"Type: Array")
                structure_section.append(f"Length: {len(data)} items")

                # Sample the first few items to determine structure
                if len(data) > 0:
                    sample_item = data[0] if len(data) > 0 else None
                    if sample_item and isinstance(sample_item, dict):
                        # Get keys from the first item
                        keys = list(sample_item.keys())
                        structure_section.append(f"Array contains objects with keys: {', '.join(keys[:10])}")
                        if len(keys) > 10:
                            structure_section.append(f"[{len(keys) - 10} more keys not shown]")
                    elif sample_item and isinstance(sample_item, list):
                        structure_section.append(f"Array contains nested arrays")
                    else:
                        structure_section.append(f"Array contains primitive values")
            else:
                structure_section.append(f"Type: Object")
                # Get top-level keys
                keys = list(data.keys())
                structure_section.append(f"Keys: {', '.join(keys[:20])}")
                if len(keys) > 20:
                    structure_section.append(f"[{len(keys) - 20} more keys not shown]")

            output_sections.append("\n".join(structure_section))

            # Format the content based on size and complexity
            content_section = ["=== JSON CONTENT ==="]

            # Helper function to truncate large nested structures
            def truncate_json(obj, max_items=20, current_depth=0, max_depth=3):
                if current_depth > max_depth:
                    return "... [nested content truncated] ..."

                if isinstance(obj, dict):
                    if len(obj) > max_items:
                        # Truncate large dictionaries
                        truncated = {}
                        for i, (k, v) in enumerate(obj.items()):
                            if i >= max_items:
                                truncated["... more items ..."] = f"[{len(obj) - max_items} items not shown]"
                                break
                            truncated[k] = truncate_json(v, max_items, current_depth + 1, max_depth)
                        return truncated
                    else:
                        # Process all items in smaller dictionaries
                        return {k: truncate_json(v, max_items, current_depth + 1, max_depth) for k, v in obj.items()}

                elif isinstance(obj, list):
                    if len(obj) > max_items:
                        # Truncate large lists
                        truncated = [truncate_json(item, max_items, current_depth + 1, max_depth) for item in obj[:max_items]]
                        truncated.append(f"... [{len(obj) - max_items} more items] ...")
                        return truncated
                    else:
                        # Process all items in smaller lists
                        return [truncate_json(item, max_items, current_depth + 1, max_depth) for item in obj]

                else:
                    # Return primitive values as is
                    return obj

            # Determine if we need to truncate based on size and complexity
            json_size = len(json.dumps(data))
            if json_size > 100000:  # Very large JSON
                # Heavily truncate
                truncated_data = truncate_json(data, max_items=10, max_depth=2)
                content_section.append("Large JSON file detected. Content has been truncated for readability.")
                content_section.append(json.dumps(truncated_data, indent=2, sort_keys=True, ensure_ascii=False))
            elif json_size > 20000:  # Medium-large JSON
                # Moderately truncate
                truncated_data = truncate_json(data, max_items=15, max_depth=3)
                content_section.append("Medium-sized JSON file. Some content has been truncated for readability.")
                content_section.append(json.dumps(truncated_data, indent=2, sort_keys=True, ensure_ascii=False))
            else:
                # Small JSON, show everything with nice formatting
                content_section.append(json.dumps(data, indent=2, sort_keys=True, ensure_ascii=False))

            output_sections.append("\n".join(content_section))

            # Add schema section for complex JSON
            if isinstance(data, dict) and len(data) > 0:
                schema_section = ["=== JSON SCHEMA OVERVIEW ==="]

                # Helper function to infer schema
                def infer_schema(obj, path="root"):
                    if isinstance(obj, dict):
                        schema = {}
                        for k, v in obj.items():
                            if isinstance(v, (dict, list)):
                                schema[k] = f"{type(v).__name__}"
                            else:
                                schema[k] = f"{type(v).__name__}"
                        return schema
                    elif isinstance(obj, list) and len(obj) > 0:
                        sample = obj[0]
                        if isinstance(sample, dict):
                            return f"Array of objects with schema: {infer_schema(sample)}"
                        else:
                            return f"Array of {type(sample).__name__}"
                    else:
                        return f"{type(obj).__name__}"

                # Add schema information
                schema_info = infer_schema(data)
                if isinstance(schema_info, dict):
                    schema_section.append("Top-level object schema:")
                    for k, v in schema_info.items():
                        schema_section.append(f"- {k}: {v}")
                else:
                    schema_section.append(f"Schema: {schema_info}")

                output_sections.append("\n".join(schema_section))

            # Return the complete formatted document
            return "\n\n".join(output_sections)

        except json.JSONDecodeError as e:
            logger.error(f"JSONDecodeError reading {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Invalid JSON format in file. {str(e)}<br><br>")
            return None
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read JSON file. {str(e)}<br><br>")
            return None

    def read_pdf(self, file_path):
        """Read PDF with optimized extraction pipeline and LLM-friendly formatting."""
        try:
            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

            # Initialize structured output
            output_sections = []

            # Add file metadata header
            output_sections.append(f"=== PDF DOCUMENT: {file_name} ===")
            output_sections.append(f"File Size: {file_size:.2f} MB")

            with fitz.open(file_path) as doc:
                # Document metadata section - minimal version
                num_pages = len(doc)

                # Only include minimal metadata at the end of the document
                metadata_section = []
                metadata_section.append(f"Total Pages: {num_pages}")

                # We'll add this at the end of the document instead of the beginning

                # Document structure section - only include if TOC is available
                structure_section = []

                # Extract table of contents if available
                toc = doc.get_toc()
                if toc:
                    structure_section = ["=== DOCUMENT STRUCTURE ==="]
                    structure_section.append("Table of Contents:")
                    for level, title, page in toc:
                        indent = "  " * (level - 1)
                        structure_section.append(f"{indent}- {title} (Page {page})")

                # Add page summary
                structure_section.append(f"\nPage Summary:")

                # Sample a few pages to determine if this is mostly text or images
                text_pages = 0
                image_pages = 0
                for i in range(min(5, num_pages)):
                    page = doc[i]
                    if page.get_text("text").strip():
                        text_pages += 1
                    else:
                        image_pages += 1

                if text_pages > image_pages:
                    structure_section.append("Document appears to be primarily text-based")
                else:
                    structure_section.append("Document appears to contain significant image content")

                # Check for forms
                has_forms = any(page.first_widget for page in doc)
                if has_forms:
                    structure_section.append("Document contains form fields")

                # Only add structure section if it has content
                if len(structure_section) > 0:
                    output_sections.append("\n".join(structure_section))

                # Content extraction - prioritize this
                max_workers = min(os.cpu_count() or 2, 4)
                content_section = ["=== DOCUMENT CONTENT ==="]

                # For very large documents, provide a summary approach
                if num_pages > 50:
                    content_section.append(f"Large document detected ({num_pages} pages). Extracting key sections:")

                    # Process first few pages, some middle pages, and last few pages
                    pages_to_process = list(range(min(5, num_pages)))  # First 5 pages

                    # Add some pages from the middle if document is large enough
                    if num_pages > 10:
                        middle_start = max(5, num_pages // 2 - 2)
                        pages_to_process.extend(range(middle_start, min(middle_start + 3, num_pages)))

                    # Add last few pages
                    if num_pages > 5:
                        pages_to_process.extend(range(max(0, num_pages - 3), num_pages))

                    # Remove duplicates and sort
                    pages_to_process = sorted(set(pages_to_process))

                    # Process selected pages
                    for page_idx in pages_to_process:
                        page = doc[page_idx]
                        page_num = page_idx + 1  # 1-based page number

                        # Extract text or perform OCR
                        text = page.get_text("text").strip()
                        if text:
                            # For long pages, extract just the beginning
                            if len(text) > 2000:
                                preview = text[:2000] + "... [content truncated]"
                                content_section.append(f"\n--- Page {page_num} (Preview) ---")
                                content_section.append(preview)
                            else:
                                content_section.append(f"\n--- Page {page_num} ---")
                                content_section.append(text)
                        else:
                            # Only attempt OCR if text extraction failed
                            try:
                                pix = page.get_pixmap(alpha=False, matrix=fitz.Matrix(1.5, 1.5))
                                with io.BytesIO(pix.tobytes("png")) as img_buffer:
                                    with Image.open(img_buffer) as image:
                                        image_copy = image.copy()

                                    ocr_text = self._perform_ocr(image_copy)
                                    cleaned_ocr_text = re.sub(r'\s+', ' ', ocr_text).strip()
                                    if cleaned_ocr_text:
                                        if len(cleaned_ocr_text) > 1000:
                                            preview = cleaned_ocr_text[:1000] + "... [content truncated]"
                                            content_section.append(f"\n--- Page {page_num} (OCR Preview) ---")
                                            content_section.append(preview)
                                        else:
                                            content_section.append(f"\n--- Page {page_num} (OCR) ---")
                                            content_section.append(cleaned_ocr_text)
                            except Exception as ocr_e:
                                logger.error(f"Error applying OCR to page {page_num}: {ocr_e}", exc_info=True)
                                content_section.append(f"Error processing page {page_num}: {str(ocr_e)}")

                    # Add note about skipped pages
                    if len(pages_to_process) < num_pages:
                        content_section.append(f"\n[{num_pages - len(pages_to_process)} pages were skipped for brevity]")

                else:
                    # For smaller documents, process all pages with parallel execution for efficiency
                    if num_pages > 5:
                        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                            # Create dictionary mapping of futures to page numbers
                            futures = {executor.submit(self._process_pdf_page_enhanced, page, page_num): page_num
                                    for page_num, page in enumerate(doc, 1)}

                            # Collect results in page order
                            page_results = {}
                            for future in concurrent.futures.as_completed(futures):
                                page_num = futures[future]
                                try:
                                    page_text = future.result()
                                    if page_text:
                                        page_results[page_num] = page_text
                                except Exception as e:
                                    logger.error(f"Error processing PDF page {page_num}: {e}", exc_info=True)
                                    page_results[page_num] = f"Error processing page {page_num}: {str(e)}"

                            # Add results in page order
                            for page_num in sorted(page_results.keys()):
                                content_section.append(f"\n--- Page {page_num} ---")
                                content_section.append(page_results[page_num])
                    else:
                        # Process pages sequentially for very small documents
                        for page_num, page in enumerate(doc, 1):
                            text = self._process_pdf_page_enhanced(page, page_num)
                            content_section.append(f"\n--- Page {page_num} ---")
                            content_section.append(text)

                output_sections.append("\n".join(content_section))

                # Extract any images if present (limited to a few for large documents)
                try:
                    image_section = ["=== DOCUMENT IMAGES ==="]
                    image_count = 0
                    max_images = 3  # Limit the number of images to describe

                    for page_idx in range(min(10, num_pages)):  # Only check first 10 pages
                        page = doc[page_idx]
                        image_list = page.get_images(full=True)

                        for img_idx, img_info in enumerate(image_list):
                            if image_count >= max_images:
                                break

                            try:
                                xref = img_info[0]
                                base_image = doc.extract_image(xref)
                                image_count += 1

                                # Get basic image info
                                width = base_image.get("width", "unknown")
                                height = base_image.get("height", "unknown")
                                image_format = base_image.get("ext", "unknown")

                                image_section.append(f"Image {image_count}: Page {page_idx+1}, Format: {image_format}, Size: {width}x{height}")
                            except Exception as img_e:
                                logger.warning(f"Error extracting image info: {img_e}")

                    if image_count > 0:
                        if image_count >= max_images:
                            image_section.append(f"[Additional images not shown]")
                        output_sections.append("\n".join(image_section))
                except Exception as img_section_e:
                    logger.warning(f"Error processing image section: {img_section_e}")

                # Add minimal metadata at the end
                if metadata_section:
                    output_sections.append("=== DOCUMENT METADATA ===\n" + "\n".join(metadata_section))

                # Return the complete formatted document
                return "\n\n".join(output_sections)
        except Exception as e:
            logger.error(f"Error reading PDF file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error reading PDF file: {str(e)}")
            return None

    def _process_pdf_page_enhanced(self, page, page_num):
        """Process a single PDF page with optimized extraction for LLM consumption."""
        try:
            text = page.get_text("text")
            if text.strip():
                # For very long pages, truncate to avoid token limits
                if len(text) > 3000:
                    return text[:3000] + "\n\n[Content truncated due to length...]"
                return text
            else:
                # Only attempt OCR if text extraction failed
                try:
                    pix = page.get_pixmap(alpha=False, matrix=fitz.Matrix(1.5, 1.5))
                    with io.BytesIO(pix.tobytes("png")) as img_buffer:
                        with Image.open(img_buffer) as image:
                            image_copy = image.copy()

                        ocr_text = self._perform_ocr(image_copy)
                        cleaned_ocr_text = re.sub(r'\s+', ' ', ocr_text).strip()

                        if cleaned_ocr_text:
                            if len(cleaned_ocr_text) > 1500:
                                return cleaned_ocr_text[:1500] + "\n\n[OCR content truncated due to length...]"
                            return cleaned_ocr_text + " [OCR]"
                        else:
                            return "[Page contains no extractable text]"
                except Exception as e:
                    return f"[OCR processing error: {str(e)}]"
        except Exception as e:
            return f"[Error processing page: {str(e)}]"

    def _process_pdf_page(self, page, page_num):
        """Process a single PDF page with optimized extraction (helper for parallel processing)."""
        result = []

        text = page.get_text("text")
        if text.strip():
            result.append(f"\nPage {page_num}:")
            result.append(text)
        else:
            try:
                pix = page.get_pixmap(alpha=False, matrix=fitz.Matrix(1.5, 1.5))
                with io.BytesIO(pix.tobytes("png")) as img_buffer:
                    with Image.open(img_buffer) as image:
                        image_copy = image.copy()

                    ocr_text = self._perform_ocr(image_copy)
                    cleaned_ocr_text = re.sub(r'\s+', ' ', ocr_text).strip()
                    if cleaned_ocr_text:
                        result.append(f"\nPage {page_num} (OCR):")
                        result.append(cleaned_ocr_text)
            except Exception as e:
                raise Exception(f"OCR processing error: {str(e)}")

        return result

    def read_word_document(self, file_path):
        """Read Word document with enhanced extraction capabilities for complex documents,
           optimized for LLM consumption with structured formatting."""
        try:
            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

            # Initialize structured output
            output_sections = []

            # Add file metadata header
            output_sections.append(f"=== WORD DOCUMENT: {file_name} ===")
            output_sections.append(f"File Size: {file_size:.2f} MB")

            doc = docx.Document(file_path)

            # Document metadata section - minimal version to be added at the end
            metadata_section = []

            # Extract only essential metadata that might be relevant for content understanding
            try:
                core_props = doc.core_properties
                if core_props.title:
                    metadata_section.append(f"Title: {core_props.title}")
                if core_props.author:
                    metadata_section.append(f"Author: {core_props.author}")
                if core_props.keywords:
                    metadata_section.append(f"Keywords: {core_props.keywords}")
            except Exception as prop_e:
                logger.debug(f"Unable to read document properties: {prop_e}")

            # We'll add this at the end of the document instead of the beginning

            # Document structure section - focus on document outline
            structure_section = []

            # Count paragraphs, tables, images (for internal use)
            paragraph_count = len(doc.paragraphs)
            table_count = len(doc.tables)

            # Count images
            image_count = 0
            try:
                image_rels = [rel for rel in doc.part.rels.values() if "image" in rel.target_ref]
                image_count = len(image_rels)
            except Exception as img_count_e:
                logger.warning(f"Error counting images: {img_count_e}")

            # Count sections
            section_count = len(doc.sections)

            # Analyze headings to create a document outline
            headings = []
            for i, para in enumerate(doc.paragraphs):
                if para.text.strip() and para.style.name.startswith('Heading'):
                    level = int(para.style.name.replace('Heading', '')) if para.style.name[7:].isdigit() else 1
                    headings.append((level, para.text.strip()))

            # Only add structure section if we have headings
            if headings:
                structure_section = ["=== DOCUMENT STRUCTURE ==="]
                structure_section.append("Document Outline:")
                for level, text in headings:
                    indent = "  " * (level - 1)
                    structure_section.append(f"{indent}- {text}")

                # Add to output sections
                output_sections.append("\n".join(structure_section))

            # Document content section
            content_section = ["=== DOCUMENT CONTENT ==="]

            # Extract paragraphs with better formatting
            paragraphs_text = []
            current_heading = None

            for i, para in enumerate(doc.paragraphs):
                if para.text.strip():
                    # Check if this is a heading
                    if para.style.name.startswith('Heading'):
                        level = int(para.style.name.replace('Heading', '')) if para.style.name[7:].isdigit() else 1
                        heading_prefix = "#" * level
                        current_heading = f"\n{heading_prefix} {para.text}"
                        paragraphs_text.append(current_heading)
                    else:
                        # Check for list formatting
                        if para.style.name.startswith('List'):
                            paragraphs_text.append(f"- {para.text}")
                        else:
                            # Regular paragraph
                            paragraphs_text.append(para.text)

            # For very long documents, truncate content
            if len(paragraphs_text) > 200:
                content_preview = paragraphs_text[:100]
                content_preview.append("\n[...Document content truncated for brevity...]")
                content_preview.extend(paragraphs_text[-50:])
                content_section.append("\n\n".join(content_preview))
            else:
                content_section.append("\n\n".join(paragraphs_text))

            output_sections.append("\n".join(content_section))

            # Tables section (if any)
            if table_count > 0:
                tables_section = ["=== TABLES ==="]

                # Limit to first 5 tables for very large documents
                max_tables = min(5, table_count)
                if max_tables < table_count:
                    tables_section.append(f"Document contains {table_count} tables. Showing first {max_tables}:")

                for table_idx, table in enumerate(doc.tables[:max_tables], 1):
                    tables_section.append(f"\nTable {table_idx}:")

                    # Get column count for this table
                    max_cols = 0
                    for row in table.rows:
                        max_cols = max(max_cols, len(row.cells))

                    # Create header separator
                    if max_cols > 0:
                        # Create markdown-style table formatting
                        header_cells = []

                        # Get header row if available
                        if len(table.rows) > 0:
                            for cell_idx, cell in enumerate(table.rows[0].cells):
                                cell_text = " ".join(p.text.strip() for p in cell.paragraphs if p.text.strip())
                                header_cells.append(cell_text or f"Column {cell_idx+1}")
                        else:
                            # No rows, create generic headers
                            for i in range(max_cols):
                                header_cells.append(f"Column {i+1}")

                        # Add header row only (no separator row with dashes)
                        tables_section.append("| " + " | ".join(header_cells) + " |")

                        # Add data rows (skip first row if it was used as header)
                        start_row = 1 if len(table.rows) > 1 else 0

                        # Limit rows for large tables
                        max_rows = 20
                        rows_to_process = table.rows[start_row:start_row+max_rows]

                        for row in rows_to_process:
                            row_cells = []
                            for cell in row.cells:
                                cell_text = " ".join(p.text.strip() for p in cell.paragraphs if p.text.strip())
                                row_cells.append(cell_text or "")

                            # Pad with empty cells if needed
                            while len(row_cells) < max_cols:
                                row_cells.append("")

                            tables_section.append("| " + " | ".join(row_cells) + " |")

                        # Add note if table was truncated
                        if len(table.rows) - start_row > max_rows:
                            tables_section.append(f"\n[Table truncated, {len(table.rows) - start_row - max_rows} more rows not shown]")

                # Add note if tables were skipped
                if table_count > max_tables:
                    tables_section.append(f"\n[{table_count - max_tables} additional tables not shown]")

                output_sections.append("\n".join(tables_section))

            # Headers and Footers section
            headers_footers = []
            try:
                for section_idx, section in enumerate(doc.sections, 1):
                    section_headers_footers = []

                    # Process headers
                    for header_type in ['header', 'first_page_header', 'even_page_header']:
                        header = getattr(section, header_type)
                        if header and header.is_linked_to_previous == False:
                            header_text = " ".join(p.text.strip() for p in header.paragraphs if p.text.strip())
                            if header_text:
                                section_headers_footers.append(f"{header_type.replace('_', ' ').title()}: {header_text}")

                    # Process footers
                    for footer_type in ['footer', 'first_page_footer', 'even_page_footer']:
                        footer = getattr(section, footer_type)
                        if footer and footer.is_linked_to_previous == False:
                            footer_text = " ".join(p.text.strip() for p in footer.paragraphs if p.text.strip())
                            if footer_text:
                                section_headers_footers.append(f"{footer_type.replace('_', ' ').title()}: {footer_text}")

                    if section_headers_footers:
                        headers_footers.append(f"Section {section_idx}:")
                        headers_footers.extend(section_headers_footers)
            except Exception as hf_e:
                logger.warning(f"Error extracting headers/footers: {hf_e}")

            if headers_footers:
                output_sections.append("=== HEADERS & FOOTERS ===\n" + "\n".join(headers_footers))

            # Images section
            if image_count > 0:
                images_section = ["=== IMAGES ==="]
                images_section.append(f"Document contains {image_count} images")

                # Process a limited number of images with OCR
                max_images_to_process = min(3, image_count)

                if max_images_to_process > 0:
                    images_section.append("Image Content Preview (OCR):")

                    image_rels = [rel for rel in doc.part.rels.values() if "image" in rel.target_ref][:max_images_to_process]

                    for idx, rel in enumerate(image_rels, 1):
                        try:
                            ocr_result = self._process_word_image(rel)
                            if ocr_result:
                                # Truncate very long OCR results
                                if len(ocr_result) > 500:
                                    ocr_preview = ocr_result[:500] + "... [OCR text truncated]"
                                    images_section.append(f"Image {idx}: {ocr_preview}")
                                else:
                                    images_section.append(f"Image {idx}: {ocr_result}")
                            else:
                                images_section.append(f"Image {idx}: [No text detected]")
                        except Exception as img_e:
                            logger.warning(f"Error processing image {idx}: {img_e}")
                            images_section.append(f"Image {idx}: [Error processing image]")

                if image_count > max_images_to_process:
                    images_section.append(f"[{image_count - max_images_to_process} additional images not processed]")

                output_sections.append("\n".join(images_section))

            # XML fallback section (only if needed)
            if paragraph_count == 0 and table_count == 0:
                try:
                    xml_section = ["=== XML EXTRACTED TEXT ==="]
                    xml_section.append("No standard content found. Attempting direct XML extraction:")

                    xml_text_elements = []
                    for element in doc.element.body.iter():
                        if element.text and element.text.strip():
                            xml_text_elements.append(element.text.strip())

                    if xml_text_elements:
                        # Limit the number of elements to avoid overwhelming the LLM
                        max_elements = 100
                        if len(xml_text_elements) > max_elements:
                            xml_preview = xml_text_elements[:max_elements]
                            xml_preview.append(f"[{len(xml_text_elements) - max_elements} more XML elements not shown]")
                            xml_section.append("\n".join(xml_preview))
                        else:
                            xml_section.append("\n".join(xml_text_elements))
                    else:
                        xml_section.append("No text content found in XML structure")

                    output_sections.append("\n".join(xml_section))
                except Exception as xml_e:
                    logger.warning(f"XML extraction fallback failed: {xml_e}")

            # Add minimal metadata at the end if available
            if metadata_section:
                output_sections.append("=== DOCUMENT METADATA ===\n" + "\n".join(metadata_section))

            # Join all sections with double newlines for better readability
            return "\n\n".join(output_sections)

        except Exception as e:
            # Catch potential docx loading errors or other unexpected issues
            logger.error(f"Critical error reading Word document {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read Word document. Check logs for details. {str(e)}")
            return None

    def _process_word_image(self, rel):
        """Process Word document image with enhanced OCR and preprocessing."""
        try:
            image_part = rel.target_part
            # Use context managers to ensure resources are released
            with io.BytesIO(image_part.blob) as image_data:
                with Image.open(image_data) as image:
                    # ENHANCED: Apply image preprocessing to improve OCR results
                    try:
                        # Convert to grayscale for better OCR
                        gray_image = image.convert('L')

                        # Enhance contrast to make text more visible
                        enhancer = ImageEnhance.Contrast(gray_image)
                        enhanced_image = enhancer.enhance(1.5)  # Increase contrast by 50%

                        # Resize if image is very small
                        if enhanced_image.width < 300 or enhanced_image.height < 300:
                            scale_factor = max(300 / enhanced_image.width, 300 / enhanced_image.height)
                            new_size = (int(enhanced_image.width * scale_factor),
                                        int(enhanced_image.height * scale_factor))
                            enhanced_image = enhanced_image.resize(new_size, Image.LANCZOS)

                        image_copy = enhanced_image.copy()
                    except Exception as preprocess_e:
                        logger.warning(f"Image preprocessing failed, using original image: {preprocess_e}")
                        image_copy = image.copy()

                # ENHANCED: Try multiple OCR configurations for better results
                ocr_text = self._perform_ocr(image_copy)

                # If OCR failed or returned very little text, try with different settings
                if not ocr_text or len(ocr_text.strip()) < 10:
                    try:
                        # Get tessdata directory
                        tessdata_dir = os.path.abspath(os.path.join(veritas_docs_dir, 'Tesseract-OCR', 'tessdata'))
                        # Set environment variable directly
                        os.environ['TESSDATA_PREFIX'] = tessdata_dir

                        # Try with different page segmentation mode without tessdata path in config
                        config = '-l eng --psm 4'  # Assume single column of text
                        ocr_text = pytesseract.image_to_string(image_copy, config=config, timeout=30)
                        logger.info("OCR with PSM 4 in word image processing succeeded")

                        # If still no good results, try one more configuration
                        if not ocr_text or len(ocr_text.strip()) < 10:
                            config = '-l eng --psm 6 --oem 1'  # Assume single block of text
                            ocr_text = pytesseract.image_to_string(image_copy, config=config, timeout=30)
                            logger.info("OCR with PSM 6 and OEM 1 in word image processing succeeded")
                    except Exception as alt_ocr_e:
                        logger.warning(f"Alternative OCR configurations failed: {alt_ocr_e}")

                return ocr_text.strip() if ocr_text else None
        except Exception as e:
            logger.error(f"Error processing image for OCR: {e}", exc_info=True)
            return None

    def read_powerpoint_document(self, file_path):
        """Read PowerPoint with optimized text extraction and LLM-friendly formatting."""
        try:
            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

            # Initialize structured output
            output_sections = []

            # Add file metadata header
            output_sections.append(f"=== POWERPOINT PRESENTATION: {file_name} ===")
            output_sections.append(f"File Size: {file_size:.2f} MB")

            ppt = Presentation(file_path)

            # Document structure section
            structure_section = ["=== PRESENTATION STRUCTURE ==="]

            # Count slides
            slide_count = len(ppt.slides)
            structure_section.append(f"Total Slides: {slide_count}")

            # Get presentation properties if available - store for later
            metadata_section = []
            try:
                core_props = ppt.core_properties
                if core_props:
                    if hasattr(core_props, 'title') and core_props.title:
                        metadata_section.append(f"Title: {core_props.title}")
                    if hasattr(core_props, 'author') and core_props.author:
                        metadata_section.append(f"Author: {core_props.author}")
                    if hasattr(core_props, 'keywords') and core_props.keywords:
                        metadata_section.append(f"Keywords: {core_props.keywords}")
            except Exception as props_e:
                logger.warning(f"Error extracting presentation properties: {props_e}")

            # Create slide outline
            structure_section.append("\nSlide Outline:")

            # Extract slide titles for outline
            slide_titles = []
            for slide_idx, slide in enumerate(ppt.slides, 1):
                title = "Untitled Slide"
                # Try to find title from title placeholder
                try:
                    for shape in slide.shapes:
                        if hasattr(shape, 'is_placeholder') and shape.is_placeholder:
                            if hasattr(shape, 'placeholder_format') and shape.placeholder_format.type == 1:  # Title placeholder
                                if hasattr(shape, 'text_frame') and shape.text_frame.text:
                                    title = shape.text_frame.text.strip()
                                    break
                except Exception:
                    pass

                # If no title found, try to get first text as title
                if title == "Untitled Slide":
                    try:
                        for shape in slide.shapes:
                            if hasattr(shape, 'has_text_frame') and shape.has_text_frame:
                                if shape.text_frame.text.strip():
                                    title = shape.text_frame.text.strip()
                                    # Truncate long titles
                                    if len(title) > 50:
                                        title = title[:47] + "..."
                                    break
                    except Exception:
                        pass

                slide_titles.append(f"Slide {slide_idx}: {title}")

            structure_section.extend(slide_titles)
            output_sections.append("\n".join(structure_section))

            # Slide content section
            content_section = ["=== SLIDE CONTENTS ==="]

            # Limit number of slides to process for very large presentations
            max_slides_to_process = min(30, slide_count)
            if max_slides_to_process < slide_count:
                content_section.append(f"Large presentation detected. Showing content from {max_slides_to_process} of {slide_count} slides.")

            # Process each slide
            for slide_idx, slide in enumerate(ppt.slides[:max_slides_to_process], 1):
                slide_content = [f"\n--- SLIDE {slide_idx} ---"]

                # Extract title if available
                title = None
                try:
                    for shape in slide.shapes:
                        if hasattr(shape, 'is_placeholder') and shape.is_placeholder:
                            if hasattr(shape, 'placeholder_format') and shape.placeholder_format.type == 1:  # Title placeholder
                                if hasattr(shape, 'text_frame') and shape.text_frame.text:
                                    title = shape.text_frame.text.strip()
                                    break
                except Exception:
                    pass

                if title:
                    slide_content.append(f"Title: {title}")

                # Extract text from all shapes
                shape_texts = []
                try:
                    for shape_idx, shape in enumerate(slide.shapes):
                        if hasattr(shape, 'has_text_frame') and shape.has_text_frame:
                            if hasattr(shape.text_frame, 'text') and shape.text_frame.text.strip():
                                # Skip if this is the title we already added
                                if shape.text_frame.text.strip() != title:
                                    # Get text with paragraph formatting
                                    paragraphs = []
                                    for para in shape.text_frame.paragraphs:
                                        if para.text.strip():
                                            # Check for bullet points
                                            if hasattr(para, 'level') and para.level > 0:
                                                indent = "  " * para.level
                                                paragraphs.append(f"{indent}- {para.text.strip()}")
                                            else:
                                                paragraphs.append(para.text.strip())

                                    if paragraphs:
                                        shape_texts.append("\n".join(paragraphs))
                except Exception as shape_e:
                    logger.warning(f"Error extracting text from shapes on slide {slide_idx}: {shape_e}")

                if shape_texts:
                    slide_content.append("Content:")
                    slide_content.extend(shape_texts)
                else:
                    slide_content.append("[No text content found]")

                # Extract notes if available
                try:
                    if (hasattr(slide, 'has_notes_slide') and slide.has_notes_slide and
                        hasattr(slide, 'notes_slide') and slide.notes_slide and
                        hasattr(slide.notes_slide, 'notes_text_frame') and
                        slide.notes_slide.notes_text_frame):

                        notes_text = slide.notes_slide.notes_text_frame.text
                        if notes_text and notes_text.strip():
                            slide_content.append(f"\nNotes: {notes_text.strip()}")
                except Exception as notes_e:
                    logger.warning(f"Error extracting notes from slide {slide_idx}: {notes_e}")

                # Add slide content to section
                content_section.append("\n".join(slide_content))

            # Add note if slides were skipped
            if slide_count > max_slides_to_process:
                content_section.append(f"\n[{slide_count - max_slides_to_process} additional slides not shown]")

            output_sections.append("\n".join(content_section))

            # Add minimal metadata at the end if available
            if metadata_section:
                output_sections.append("=== PRESENTATION METADATA ===\n" + "\n".join(metadata_section))

            # Return the complete formatted document
            return "\n\n".join(output_sections)
        except Exception as e:
            logger.error(f"Error reading PowerPoint file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read PowerPoint file. {str(e)}")
            return None

    def read_excel_file(self, file_path):
        """Read Excel file with optimized pandas configuration and memory usage."""
        try:
            # Configure pandas options for optimal memory usage
            pandas_options = {
                'sheet_name': None,
                'engine': 'openpyxl',
                'dtype': object,
                'na_filter': False,
                'verbose': False
            }

            # Add read_only parameter for openpyxl engine if available
            # This is supported by openpyxl engine in newer pandas versions
            try:
                pandas_options['engine_kwargs'] = {'read_only': True}
            except Exception as e:
                logger.warning(f"Could not set read_only mode for Excel: {e}")

            # Read Excel file directly without the deprecated option context
            excel_data = pd.read_excel(file_path, **pandas_options)

            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

            # Process sheets efficiently with list comprehension
            formatted_sheets = []
            sheet_summaries = []

            # Add file metadata header
            formatted_sheets.append(f"=== EXCEL FILE: {file_name} ===")
            formatted_sheets.append(f"File Size: {file_size:.2f} MB")
            formatted_sheets.append(f"Total Sheets: {len(excel_data)}")
            formatted_sheets.append("")

            for sheet_name, df in excel_data.items():
                # Create summary for this sheet
                num_rows = len(df)
                num_cols = len(df.columns)
                sheet_summaries.append(f"- Sheet '{sheet_name}': {num_rows} rows × {num_cols} columns")

                if df.empty:
                    formatted_sheets.append(f"\n=== Sheet: {sheet_name} ===\n[Empty sheet]")
                    continue

                # Format column headers more clearly (no separator row)
                headers = "| " + " | ".join(str(col) for col in df.columns) + " |"

                # Handle large dataframes efficiently
                with pd.option_context('display.max_rows', None, 'display.max_columns', None):
                    sheet_header = f"\n=== Sheet: {sheet_name} ===\n"
                    sheet_header += f"Rows: {num_rows}, Columns: {num_cols}\n"
                    sheet_header += f"Column Names: {', '.join(str(col) for col in df.columns)}\n"

                    # Detect data types in columns
                    data_types = {}
                    for col in df.columns:
                        try:
                            # Get the most common data type in the column
                            non_null_values = df[col].dropna()
                            if len(non_null_values) > 0:
                                sample = non_null_values.iloc[0]
                                data_type = type(sample).__name__
                                data_types[col] = data_type
                        except:
                            data_types[col] = "unknown"

                    sheet_header += f"Data Types: {', '.join(f'{col}: {dtype}' for col, dtype in data_types.items())}\n\n"

                    # Add table header (no separator row with dashes)
                    sheet_header += headers + "\n"

                    if len(df) > 1000:
                        # For large sheets, show first and last rows with clear separation
                        head_df = df.head(20)
                        tail_df = df.tail(20)

                        # Format rows as table rows
                        head_rows = []
                        for _, row in head_df.iterrows():
                            row_str = "| " + " | ".join(str(val) for val in row) + " |"
                            head_rows.append(row_str)

                        tail_rows = []
                        for _, row in tail_df.iterrows():
                            row_str = "| " + " | ".join(str(val) for val in row) + " |"
                            tail_rows.append(row_str)

                        sheet_text = (
                            sheet_header +
                            "\n".join(head_rows) +
                            f"\n\n[...{num_rows - 40} more rows...]\n\n" +
                            "\n".join(tail_rows)
                        )
                    else:
                        # For smaller sheets, show all rows
                        rows = []
                        for _, row in df.iterrows():
                            row_str = "| " + " | ".join(str(val) for val in row) + " |"
                            rows.append(row_str)

                        sheet_text = sheet_header + "\n".join(rows)

                    formatted_sheets.append(sheet_text)

            # Add sheet summaries at the top for better navigation
            formatted_sheets.insert(4, "=== SHEET SUMMARY ===")
            for summary in sheet_summaries:
                formatted_sheets.insert(5 + sheet_summaries.index(summary), summary)
            formatted_sheets.insert(5 + len(sheet_summaries), "")

            # Handle empty case
            if len(formatted_sheets) <= 5:  # Only metadata headers
                return "No data found in Excel file."

            return "\n".join(formatted_sheets)
        except Exception as e:
            logger.error(f"Error reading Excel file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read Excel file. {str(e)}")
            return None

    def read_csv_file(self, file_path):
        """Read CSV file with optimized structure preservation for LLM processing."""
        try:
            # First, try to detect the delimiter by reading a few lines
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                sample = ''.join(f.readline() for _ in range(5))

            # Count potential delimiters
            delimiters = [',', ';', '\t', '|']
            delimiter_counts = {d: sample.count(d) for d in delimiters}
            likely_delimiter = max(delimiter_counts, key=delimiter_counts.get)

            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

            # Try to read with the detected delimiter
            try:
                # First attempt to infer header
                try:
                    # For newer pandas versions (1.3+)
                    df = pd.read_csv(file_path, delimiter=likely_delimiter, dtype=object,
                                    encoding='utf-8', engine='python', on_bad_lines='skip')
                except TypeError:
                    # For older pandas versions
                    df = pd.read_csv(file_path, delimiter=likely_delimiter, dtype=object,
                                    encoding='utf-8', engine='python', error_bad_lines=False,
                                    warn_bad_lines=False)

                # Check if header inference was likely correct
                has_header = True
                # If first row contains all numeric values while column names are strings,
                # it might not be a header
                if all(pd.to_numeric(df.iloc[0], errors='coerce').notna()):
                    if all(isinstance(col, str) for col in df.columns):
                        has_header = False

                # If we suspect no header, read again
                if not has_header:
                    try:
                        # For newer pandas versions (1.3+)
                        df = pd.read_csv(file_path, delimiter=likely_delimiter, dtype=object,
                                        encoding='utf-8', engine='python', header=None,
                                        on_bad_lines='skip')
                    except TypeError:
                        # For older pandas versions
                        df = pd.read_csv(file_path, delimiter=likely_delimiter, dtype=object,
                                        encoding='utf-8', engine='python', header=None,
                                        error_bad_lines=False, warn_bad_lines=False)
            except:
                # Fallback to a more permissive reading
                try:
                    # For newer pandas versions (1.3+)
                    df = pd.read_csv(file_path, delimiter=None, dtype=object, encoding='utf-8',
                                    engine='python', on_bad_lines='skip')
                except TypeError:
                    # For older pandas versions
                    df = pd.read_csv(file_path, delimiter=None, dtype=object, encoding='utf-8',
                                    engine='python', error_bad_lines=False, warn_bad_lines=False)

            # Format the output
            num_rows = len(df)
            num_cols = len(df.columns)

            formatted_output = []

            # Add file metadata
            formatted_output.append(f"=== CSV FILE: {file_name} ===")
            formatted_output.append(f"File Size: {file_size:.2f} MB")
            formatted_output.append(f"Rows: {num_rows}, Columns: {num_cols}")
            formatted_output.append(f"Delimiter: '{likely_delimiter}'")
            formatted_output.append(f"Column Names: {', '.join(str(col) for col in df.columns)}")

            # Detect data types in columns
            data_types = {}
            for col in df.columns:
                try:
                    # Get the most common data type in the column
                    non_null_values = df[col].dropna()
                    if len(non_null_values) > 0:
                        sample = non_null_values.iloc[0]
                        data_type = type(sample).__name__
                        data_types[col] = data_type
                except:
                    data_types[col] = "unknown"

            formatted_output.append(f"Data Types: {', '.join(f'{col}: {dtype}' for col, dtype in data_types.items())}")
            formatted_output.append("")

            # Format column headers more clearly (no separator row)
            headers = "| " + " | ".join(str(col) for col in df.columns) + " |"

            # Only add the header row, no separator row with dashes
            formatted_output.append(headers)

            # Format data rows
            if num_rows > 100:
                # For large files, show first and last rows
                head_df = df.head(20)
                tail_df = df.tail(20)

                # Format rows as table rows
                for _, row in head_df.iterrows():
                    row_str = "| " + " | ".join(str(val) for val in row) + " |"
                    formatted_output.append(row_str)

                formatted_output.append(f"\n[...{num_rows - 40} more rows...]\n")

                for _, row in tail_df.iterrows():
                    row_str = "| " + " | ".join(str(val) for val in row) + " |"
                    formatted_output.append(row_str)
            else:
                # For smaller files, show all rows
                for _, row in df.iterrows():
                    row_str = "| " + " | ".join(str(val) for val in row) + " |"
                    formatted_output.append(row_str)

            return "\n".join(formatted_output)
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read CSV file. {str(e)}")
            return None

    def read_parquet_file(self, file_path):
        """Read Parquet file with optimized structure preservation for LLM processing."""
        try:
            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB

            # Read the parquet file
            df = pd.read_parquet(file_path)

            # Format the output
            num_rows = len(df)
            num_cols = len(df.columns)

            formatted_output = []

            # Add file metadata
            formatted_output.append(f"=== PARQUET FILE: {file_name} ===")
            formatted_output.append(f"File Size: {file_size:.2f} MB")
            formatted_output.append(f"Rows: {num_rows}, Columns: {num_cols}")
            formatted_output.append(f"Column Names: {', '.join(str(col) for col in df.columns)}")

            # Get column data types
            data_types = {col: str(df[col].dtype) for col in df.columns}
            formatted_output.append(f"Data Types: {', '.join(f'{col}: {dtype}' for col, dtype in data_types.items())}")
            formatted_output.append("")

            # Format column headers more clearly (no separator row)
            headers = "| " + " | ".join(str(col) for col in df.columns) + " |"

            # Only add the header row, no separator row with dashes
            formatted_output.append(headers)

            # Format data rows
            if num_rows > 100:
                # For large files, show first and last rows
                head_df = df.head(20)
                tail_df = df.tail(20)

                # Format rows as table rows
                for _, row in head_df.iterrows():
                    row_str = "| " + " | ".join(str(val) for val in row) + " |"
                    formatted_output.append(row_str)

                formatted_output.append(f"\n[...{num_rows - 40} more rows...]\n")

                for _, row in tail_df.iterrows():
                    row_str = "| " + " | ".join(str(val) for val in row) + " |"
                    formatted_output.append(row_str)
            else:
                # For smaller files, show all rows
                for _, row in df.iterrows():
                    row_str = "| " + " | ".join(str(val) for val in row) + " |"
                    formatted_output.append(row_str)

            return "\n".join(formatted_output)
        except Exception as e:
            logger.error(f"Error reading Parquet file {file_path}: {e}", exc_info=True)
            self.communicator.response_received.emit(f"Error: Could not read Parquet file. {str(e)}")
            return None

# DETERMINE INPUT AND CREATE THREAD ##################################################################################################################################################################################################
    def _determine_input_type_and_create_thread(self, user_input, is_ai=False):
        # Store the latest user input for table detection
        self.latest_user_input = user_input

        url_pattern = re.compile(r'(https?://\S+)')
        url_match = url_pattern.search(user_input.strip())

        if url_match:
            detected_url = url_match.group(1)
            # Check if URL is at the beginning or if there are instructions after the URL
            if user_input.strip().startswith(detected_url) or url_match.start() == 0:
                logger.debug(f"URL input detected: '{detected_url}' with user input: '{user_input}'")
                thread = threading.Thread(target=self.handle_url_input, args=(detected_url, user_input))
                return thread
            else:
                logger.debug("URL found within text, treating as normal text input.")
                thread = threading.Thread(target=self.generate_response_thread, args=(user_input,))
                return thread



        search_pattern = re.compile(r'^/search\s+(.+)$', re.IGNORECASE)
        search_match = search_pattern.match(user_input.strip())

        if search_match:
            search_query = search_match.group(1).strip()
            if search_query.lower() in self.recent_searches:
                logger.debug(f"Search query '{search_query}' already processed. Skipping redundant search.")
                return None

            logger.debug(f"Search command detected with query: {search_query}")
            self.recent_searches.add(search_query.lower())
            self.search_triggered = True
            thread = threading.Thread(target=self.handle_search_query, args=(search_query,))
            return thread

        else:
            if is_ai:
                logger.debug("AI-triggered normal conversation input detected")
            else:
                logger.debug("User-triggered normal conversation input detected")
            thread = threading.Thread(target=self.generate_response_thread, args=(user_input,))
            return thread



# COMMANDS SECTION FOR ANY AI ADDITIONAL FEATURES#####################################################################################################################################################################################

    def fix_numbered_lists(self, text):
        # Split the text into lines
        lines = text.split('\n')

        # Find all numbered list items at the same indentation level
        i = 0
        while i < len(lines):
            # Look for a line that starts with a number (especially "1.")
            match = re.match(r'^(\s*)(\d+)([.)])\s+(.+)$', lines[i])
            if match:
                indent, number, delimiter, content = match.groups()

                # Start tracking this list
                list_items = [(i, number, delimiter, content)]

                # Look for more items with the same indentation level
                j = i + 1
                while j < len(lines):
                    # Skip bullet points and blank lines
                    if not lines[j].strip():
                        j += 1
                        continue

                    if lines[j].strip().startswith('*') or lines[j].strip().startswith('-') or lines[j].strip().startswith('•'):
                        j += 1
                        continue

                    # Check if this is another numbered item at the same indentation level
                    next_match = re.match(r'^(\s*)(\d+)([.)])\s+(.+)$', lines[j])
                    if next_match and next_match.group(1) == indent:
                        # Found another numbered item
                        list_items.append((j, next_match.group(2), next_match.group(3), next_match.group(4)))
                        j += 1
                    else:
                        # Not a numbered item at this level, end of the list
                        break

                # If we found multiple items with the same indentation, check if they need fixing
                if len(list_items) > 1:
                    # Check if all items have the same number
                    numbers = [item[1] for item in list_items]
                    if len(set(numbers)) == 1:
                        # All items have the same number, fix the numbering
                        for idx, (line_idx, _, delimiter, content) in enumerate(list_items, 1):
                            lines[line_idx] = f"{indent}{idx}{delimiter} {content}"

                # Move to the next item after this list
                i = j
            else:
                i += 1

        return '\n'.join(lines)

    def _process_ai_commands(self, formatted_response, user_input, current_time):
        # Fix numbered lists that have bullet points between them
        try:
            formatted_response = self.fix_numbered_lists(formatted_response)
            # logger.debug("Applied fix_numbered_lists to response") # Optional: reduce log verbosity
        except Exception as e:
            logger.error(f"Error applying fix_numbered_lists: {e}")

        # Pass the original user_input (AI's current task prompt) to _handle_search_command
        formatted_response = self._handle_search_command(formatted_response, user_input)
        formatted_response = self._handle_memory_command(formatted_response)
        formatted_response = self._handle_db_title_command(formatted_response)
        return formatted_response



    def _handle_search_command(self, formatted_response, original_ai_task_input):
        """
        Handles the /search command found in the AI's response.
        Prevents re-searching if the AI's current task was to analyze search results.
        Args:
            formatted_response: The AI's generated response text.
            original_ai_task_input: The specific input/prompt given to the AI for this response generation.
        Returns:
            The response text, potentially cleaned of search commands if they were processed or ignored.
        """
        search_matches = re.findall(r'^/search\s+(.+)$', formatted_response, flags=re.IGNORECASE | re.MULTILINE)

        if not search_matches:
            return formatted_response

        if self.search_triggered:
            logger.debug("Search already triggered by user input, removing AI search commands.")
            # Even if search_triggered is true, we still want to clean the response of the /search command
            # This part handles the case where the user initiated a search, and the AI's *first* response
            # (not an analysis of results yet) might incorrectly include a search.
            formatted_response = re.sub(r'^/search\s+.*$', '', formatted_response, flags=re.MULTILINE | re.IGNORECASE).strip()
            formatted_response = re.sub(r'\n\s*\n', '\n', formatted_response) # Clean up extra newlines
            return formatted_response

        # Check if the AI's current task was to analyze search results.
        # This is a key condition to prevent re-searching during an analysis phase.
        is_analyzing_search_results = "SEARCH RESULTS:" in original_ai_task_input and \
                                      "INSTRUCTION: Analyze the provided search results" in original_ai_task_input

        if is_analyzing_search_results:
            logger.debug("AI was analyzing search results. Ignoring any new /search commands in its response.")
            formatted_response = re.sub(r'^/search\s+.*$', '', formatted_response, flags=re.MULTILINE | re.IGNORECASE).strip()
            formatted_response = re.sub(r'\n\s*\n', '\n', formatted_response)
            return formatted_response

        search_topics = [match.strip() for match in search_matches]
        if search_topics:
            logger.debug(f"AI generated {len(search_topics)} /search command(s): {search_topics}")
            cleaned_response = re.sub(r'^/search\s+.*$', '', formatted_response, flags=re.MULTILINE | re.IGNORECASE).strip()
            cleaned_response = re.sub(r'\n\s*\n', '\n', cleaned_response)
            if len(search_topics) == 1:
                search_thread = threading.Thread(target=self.handle_search_query, args=(search_topics[0],), daemon=True)
                logger.debug(f"Starting single search thread for AI query: {search_topics[0]}")
            else:
                search_thread = threading.Thread(target=self.handle_search_query, args=(search_topics,), daemon=True)
                logger.debug(f"Starting multi-search thread for AI queries: {search_topics}")

            if search_thread:
                self.search_triggered = True
                search_thread.start()
            return cleaned_response # Return the response without the search command
        return formatted_response

    def _handle_memory_command(self, formatted_response):
        """Handles the /memory command."""
        memory_matches = re.findall(r'/memory(?:\s+(.+))?', formatted_response, flags=re.IGNORECASE)
        cleaned_response = formatted_response
        if memory_matches:
            memory_updated = False
            for memory_content in memory_matches:
                if memory_content:
                    try:
                        parts = memory_content.split(':', 1)
                        if len(parts) == 2:
                            alert_key = parts[0].strip()
                            info = parts[1].strip()
                            if alert_key and info:
                                save_mind(alert_key, info)
                                logger.info(f"Memory saved: Alert='{alert_key}', Response='{info}'")
                                memory_updated = True
                            else:
                                logger.warning(f"Skipping invalid memory command format: {memory_content}")
                        else:
                             logger.warning(f"Skipping invalid memory command format (missing ':'): {memory_content}")
                    except Exception as e:
                        logger.error(f"Error processing memory command '{memory_content}': {e}")

            cleaned_response = re.sub(r'^/memory(?:\s+.+)?$', '', cleaned_response, flags=re.IGNORECASE | re.MULTILINE).strip()
            cleaned_response = re.sub(r'\n\s*\n', '\n', cleaned_response)

            if memory_updated:
                self.communicator.response_received.emit(
                    "<b><font color='yellow'> >>> Memory Updated!</font></b>"
                )

        return cleaned_response


    def _handle_db_title_command(self, formatted_response: str) -> str:
        """
        Process the /DBtitle command in the AI response.
        Sets the title for the current session's log file and triggers a backup.
        Args:
            formatted_response: The AI response text to process
        Returns:
            The response with the /DBtitle command removed
        """
        db_title_pattern = re.compile(r'/DBtitle\s+(.+?)(?:\n|$)', re.IGNORECASE | re.MULTILINE | re.DOTALL)
        match = db_title_pattern.search(formatted_response)

        cleaned_response = formatted_response

        if match:
            db_title = match.group(1).strip()
            logger.info(f"Attempting to process /DBtitle. Extracted title: '{db_title}'. Current db_title_set_for_current_session: {self.db_title_set_for_current_session}")
            if db_title:
                # Only set title and backup if it hasn't been set for this session yet
                if not self.db_title_set_for_current_session:
                    if len(db_title) > 50: # Limit title length
                        db_title = db_title[:47] + "..."
                        logger.warning(f"DB title truncated to 50 characters: {db_title}")

                    self.current_session_log_title = db_title
                    self.db_title_set_for_current_session = True # Mark that a custom title is set for this session
                    logger.info(f"Session log title set by AI (first time this session): '{db_title}' for session suffix: {self.current_session_log_suffix}")

                    # Backup the conversation history with the new title for the current session
                    self.backup_conversation_history()
                else:
                    logger.info(f"DBtitle '{db_title}' received, but a title for this session was already set. Current session title '{self.current_session_log_title}' remains. Backup not re-triggered by this command.")
            else:
                logger.warning(f"Empty title extracted from /DBtitle command. Command ignored. Full match: {match.group(0)}")

            # Always remove the /DBtitle command line from the response
            # More robust removal to handle cases where it might not be the absolute last line
            # due to other commands or accidental newlines.
            lines = cleaned_response.splitlines()
            lines_without_db_title = [line for line in lines if not db_title_pattern.match(line)]
            cleaned_response = "\n".join(lines_without_db_title).strip()
        else:
            logger.info("No /DBtitle command found in the response.")
            # Fallback: if no /DBtitle is found, but a backup might be desired (e.g. on reset_chat)
            # we ensure the current default title is used if a backup is triggered elsewhere.
            if not self.db_title_set_for_current_session:
                 self.current_session_log_title = "Veritas_User_Interaction_log" # Default

        return cleaned_response

# GENERATE RESPONSE THREAD ##################################################################################################################################################################################################
    def generate_response_thread(self, user_input, sources=None, file_name=None, image_prompt=None, current_time=None):
        self.search_triggered = False
        self._image_analysis_triggered = False
        self.chat_reset_flag = False

        loaded_history = load_conversation_history()
        conversation_history = [f"{turn[0]}: {turn[1]} ({turn[2]})" for turn in loaded_history]

        try:
            formatted_response = generate_response(user_input, conversation_history, image_data=image_prompt)

            formatted_response = self._process_ai_commands(formatted_response, user_input, current_time)

            formatted_response = remove_ai_name_mentions(formatted_response)

            save_conversation_history("Veritas", formatted_response)
            self._append_ai_response(formatted_response)
            return formatted_response

        except Exception as e:
            error_message = f"Error in generate_response_thread: {e}. Please check API key or wait a moment."
            full_error_message = f"{error_message}\n\nTraceback:\n{traceback.format_exc()}"
            self.communicator.response_received.emit(full_error_message)
            logger.error(full_error_message)

# DISPLAY TEXT ON GUI ##################################################################################################################################################################################################
    def display_text(self):
        user_input = self.input_Box.toPlainText().strip()

        # Store the latest user input for table detection
        self.latest_user_input = user_input

        # Special handling for document context waiting
        if hasattr(self, 'waiting_for_document_context') and self.waiting_for_document_context:
            if getattr(self, 'current_upload_file_path', None) or getattr(self, 'current_upload_files', None):
                self.waiting_for_document_context = False

                if user_input:
                    # Process with custom instructions
                    self._append_user_input(user_input)
                    self.process_uploaded_document(user_context=user_input)
                else:
                    # Process with default instructions when input is empty
                    logger.info("Processing document with default instructions from display_text")
                    self.communicator.deep_searching_label_updated.emit(f"<b><font color='#00FFFF'> >>> Processing with default analysis</font></b>")
                    self.process_uploaded_document(user_context='')

                if hasattr(self, 'attachment_label'):
                    self.attachment_label.hide()

                self.input_Box.clear()
                self.input_Box.setFixedHeight(self.input_Box.min_height)
                return

        # Normal text input handling (non-document context)
        if not user_input:
            return

        logger.debug(f"{user_input}")

        self._append_user_input(user_input)

        thread = self._determine_input_type_and_create_thread(user_input)
        logger.debug("Starting thread")
        thread.start()


    def update_response(self, response):
        scrollbar = self.outb.verticalScrollBar()
        pre_update_max = scrollbar.maximum()
        scrollbar.setValue(pre_update_max)
        current_html = self.outb.toHtml()

        formatted_response = self._format_response(response)
        if isinstance(formatted_response, tuple):
            formatted_response = formatted_response[0]

        new_html = current_html + formatted_response

        self.outb.setHtml(new_html)
        scrollbar.setValue(pre_update_max)
        start_value = pre_update_max
        target_value = scrollbar.maximum()

        # Fixed duration of 300ms for smooth scrolling
        duration = 1000

        if hasattr(self, "_scroll_timer") and self._scroll_timer.isActive():
            self._scroll_timer.stop()

        def ease_in_out_cubic(t):
            if t < 0.5:
                return 4 * t * t * t
            else:
                return 1 - pow(-2 * t + 2, 3) / 2

        # Start time for animation
        scroll_start_time = time.perf_counter()

        def animate_scroll():
            nonlocal scroll_start_time, start_value, target_value
            elapsed = (time.perf_counter() - scroll_start_time) * 1000
            progress = min(elapsed / duration, 1.0)
            target_value = scrollbar.maximum()
            eased_progress = ease_in_out_cubic(progress)
            new_value = start_value + (target_value - start_value) * eased_progress
            scrollbar.setValue(min(int(new_value), target_value))
            if progress >= 1.0:
                self._scroll_timer.stop()

        # Create timer with smaller interval for smoother animation
        self._scroll_timer = QTimer(self)
        self._scroll_timer.timeout.connect(animate_scroll)
        # Use a smaller interval for smoother animation with fixed 300ms duration
        self._scroll_timer.start(16)


    def _append_user_input(self, user_input):
        user_input_formatted = user_input.replace('\n', '<br>')
        formatted_message = f"<b><font color='#00FFFF'>User:</font></b><br>{user_input_formatted}"
        formatted_message = self._format_response(formatted_message)
        self.communicator.response_received.emit(formatted_message)
        save_conversation_history("User", user_input)

    def _append_ai_response(self, formatted_response):
        formatted_message = f"<b><br><font color='#00FFFF'>Veritas:</font></b><br>{formatted_response}<br>"
        formatted_message = self._format_response(formatted_message)
        self.communicator.response_received.emit(formatted_message)
    def _process_urls_in_text(self, text):
        """Process text to make URLs clickable with blue color"""
        # Use the same URL pattern as in _apply_text_formatting
        url_pattern = re.compile(r'(https?|ftp):\/\/[^\s<>"\']+[^.,!?:;\s<>"\'`)\]]')

        # Replace URLs with clickable links
        return url_pattern.sub(r'<a style="color: #61b0ee;" href="\g<0>">\g<0></a>', text)

# FORMATTING ##################################################################################################################################################################################################

    def _apply_text_formatting(self, text):
        # REMOVED: Header formatting from this basic inline formatting function
        formatting_patterns = [
            (re.compile(r'\[(.+?)\]\((.+?)\)'), r'<a style="color: #61b0ee;" href="\2">\1</a>'),
            # Basic inline formatting
            (re.compile(r'(?<!\\)\*\*(.+?)\*\*'), r'<strong style="color: #2986cc;">\1</strong>'), # Bold - Keep this
            (re.compile(r'(?<!\\)\*(.+?)\*'), r'<em>\1</em>'), # Italic - Keep this
            (re.compile(r'(?<!\\)__([^_]+?)__'), r'<u>\1</u>'), # Underline - Keep this
            (re.compile(r'~~(.+?)~~'), r'<del>\1</del>'), # Strikethrough - Keep this
            (re.compile(r'\^(.+?)\^'), r'<sup>\1</sup>'), # Superscript - Keep this
            (re.compile(r'~(.+?)~'), r'<sub>\1</sub>'), # Subscript - Keep this
            (re.compile(r'==(.+?)=='), r'<mark>\1</mark>'), # Highlight - Keep this
        ]

        temp_text = text
        for pattern, replacement in formatting_patterns:
            temp_text = pattern.sub(replacement, temp_text)
        url_pattern = re.compile(r'(?<!href=["\'])(?<!src=["\'])(?<!["\'>])(https?|ftp):\/\/[^\s<>"\']+[^.,!?:;\s<>"\'`)]')

        # Apply URL formatting directly with regex substitution
        temp_text = url_pattern.sub(r'<a style="color: #61b0ee;" href="\g<0>">\g<0></a>', temp_text)
        return temp_text

    def _format_response(self, response: str) -> str:
        """
        Formats the AI response text into HTML for display in the QTextBrowser,
        handling various markdown-like syntaxes including lists, blockquotes,
        tables, and replacing sections starting/ending with lines beginning
        with ``` with horizontal lines and applying specific font styling
        to the content within.
        """
        # Check if this is tabular data stored in JSON format
        if "TabularData" in response:
            try:
                json_start = response.find('{')
                if json_start != -1:
                    json_data = response[json_start:]
                    structured_data = json.loads(json_data)
                    return self._format_tabular_data(structured_data)
            except json.JSONDecodeError:
                pass # If JSON parsing fails, treat as normal text

        lines = response.replace('\r\n', '\n').split('\n')
        table_lines = []
        in_table = False

        # Pre-scan for potential table lines
        for i, line_content_for_table_check in enumerate(lines):
            stripped_line_for_table_check = line_content_for_table_check.strip()
            if stripped_line_for_table_check.startswith('|') and stripped_line_for_table_check.endswith('|') and '|' in stripped_line_for_table_check[1:-1]:
                if not in_table:
                    in_table = True
                table_lines.append(i)
            elif in_table and not stripped_line_for_table_check:
                in_table = False

        response_lines = response.replace('\r\n', '\n').split("\n")
        formatted_response = []
        # State includes tracking for the custom code block replacement
        state = {
            'inside_list': False,
            'inside_blockquote': False,
            'inside_numbered_list': False,
            'inside_table': False,
            'inside_code_block': False, # Tracks if we are between lines starting with ```
            'last_list_item_was_numbered': False, # Track if the last list item was numbered
            'current_list_number': 0, # Track the current number in a numbered list
        }

        # Define regex patterns
        list_item_pattern = re.compile(r'^\s*([*\-\•o])\s+(.+)$')
        numbered_list_pattern = re.compile(r'^\s*(\d+)\.\s+(.+)$')
        blockquote_pattern = re.compile(r'^\>\s*(.*)$')
        dash_line_pattern = re.compile(r'^[-—–—]{3,}$')
        # --- Modified Pattern ---
        # Detects lines starting with ```, optionally followed by a language identifier, ignoring leading whitespace
        code_block_delimiter_pattern = re.compile(r'^\s*```(?:\w*)?')

        # Style for the horizontal rule replacement
        # Using a simpler text representation as requested
        hr_replacement = "<p style='margin: 4px 0; color: #888; user-select: none;'>---------------------------------------</p>" # Use a paragraph for the line
        # Style for text within the replaced code blocks
        code_font_style = 'font-family: "JetBrains Mono", "Fira Code", "Consolas", "Courier New", monospace; margin: 0; line-height: 1.1; font-size: 12px; color: #f0f0f0;' # Adjusted font size and color

        def close_open_tags() -> None:
            """Closes any currently open HTML tags based on the state."""
            if state['inside_list']:
                formatted_response.append("</ul>")
                state['inside_list'] = False
            if state['inside_blockquote']:
                formatted_response.append("</blockquote>")
                state['inside_blockquote'] = False
            if state['inside_numbered_list']:
                formatted_response.append("</ol>")
                state['inside_numbered_list'] = False
                state['current_list_number'] = 0
                state['last_list_item_was_numbered'] = False
            if state['inside_table']:
                formatted_response.append("</table>")
                state['inside_table'] = False
                if hasattr(self, 'column_alignments'): delattr(self, 'column_alignments')
                if hasattr(self, 'saw_separator'): delattr(self, 'saw_separator')
                if hasattr(self, 'table_row_count'): delattr(self, 'table_row_count')
                if hasattr(self, 'max_table_columns'): delattr(self, 'max_table_columns')
            # If we end the response inside a code block, add the closing line
            if state['inside_code_block']:
                formatted_response.append(hr_replacement)
                state['inside_code_block'] = False

        def append_paragraph(line_content: str, margin_bottom: int = 5) -> None:
            """Appends a line as a standard formatted HTML paragraph."""
            if not line_content.strip():
                return
            formatted_text = self._apply_text_formatting(line_content)
            base_style = f'margin: 0; line-height: 0.9; margin-bottom: {margin_bottom}px;'
            formatted_response.append(
                f"<p style='{base_style}'>{formatted_text}</p>"
            )

        def append_code_line(line_content: str) -> None:
            """Appends a line specifically formatted as code content."""
            # Correct HTML escaping for special characters
            escaped_line = line_content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
            
            # If the original line is empty, use &nbsp; to ensure the <p> tag has height
            # and white-space: pre-wrap will respect it.
            # Otherwise, use the escaped line.
            display_line = escaped_line if line_content else "&nbsp;"

            # Add white-space: pre-wrap to the existing code_font_style for this paragraph
            # This preserves whitespace (spaces, tabs) and wraps long lines.
            p_style_with_pre_wrap = code_font_style + ' white-space: pre-wrap;'
            formatted_response.append(
                f"<p style='{p_style_with_pre_wrap}'>{display_line}</p>"
            )


        for line in response_lines:
            # Check if the line STARTS with ``` (ignoring leading whitespace), optionally followed by a language identifier
            is_delimiter_line = code_block_delimiter_pattern.match(line) is not None

            if is_delimiter_line:
                if state['inside_code_block']:
                    # End of the code block
                    formatted_response.append(hr_replacement) # Add closing line

                    # Get the current code block index - count completed code blocks
                    code_block_index = len([line for line in formatted_response if "---------------------------------------" in line]) // 2 - 1

                    # Add clickable "Copy" text with custom URL scheme
                    copy_button = f"""<p style='margin: 0; line-height: 0.9; margin-bottom: 5px; text-align: left;'>
                        <a href='copy:{code_block_index}' style='color: #888; text-decoration: none; cursor: pointer;'>Copy</a>
                    </p>"""
                    formatted_response.append(copy_button)
                    state['inside_code_block'] = False
                else:
                    # Start of the code block
                    close_open_tags() # Close any other tags first
                    formatted_response.append(hr_replacement) # Add starting line
                    state['inside_code_block'] = True
                continue # Skip processing the delimiter line itself

            # Handle lines inside the custom code block
            if state['inside_code_block']:
                append_code_line(line) # Append the line with code styling
                continue

            # --- Standard Formatting Logic (Outside Code Blocks) ---
            stripped_line_logic = line.strip() # Used for standard formatting checks

            # Handle empty lines (outside code blocks)
            if not stripped_line_logic:
                if state['inside_list'] or state['inside_blockquote'] or state['inside_numbered_list'] or state['inside_table']:
                    close_open_tags()
                continue

            # Handle blockquotes
            blockquote_match = blockquote_pattern.match(line)
            if blockquote_match:
                content = blockquote_match.group(1)
                if not state['inside_blockquote']:
                    close_open_tags()
                    formatted_response.append("<blockquote style='line-height: 0.9; padding-left: 10px; margin-left: 5px; border-left: 2px solid #ccc; margin-top: 3px; margin-bottom: 3px;'>")
                    state['inside_blockquote'] = True
                append_paragraph(content, margin_bottom=1)
                continue
            elif state['inside_blockquote']:
                close_open_tags()

            # Handle unordered lists
            list_match = list_item_pattern.match(line)
            if list_match:
                _, content = list_match.groups()
                if content.strip().endswith(':'):
                    close_open_tags()
                    append_paragraph(content)
                    continue

                # Check if this bullet point is part of a numbered list item
                if state['last_list_item_was_numbered']:
                    # This is a bullet point following a numbered item, keep the numbered list open
                    if not state['inside_list']:
                        formatted_response.append("<ul style='margin: 0; padding-left: 25px; line-height: 0.9; margin-bottom: 8px;'>")
                        state['inside_list'] = True
                    formatted_response.append(f"<li style='margin: 0; padding-left: 5px; line-height: 0.9; margin-bottom: 4px;'>{self._apply_text_formatting(content)}</li>")
                else:
                    # This is a regular bullet point
                    if state['inside_numbered_list']:
                        # Don't close the numbered list yet, as this might be a bullet under a numbered item
                        pass

                    if not state['inside_list']:
                        # Only close other tags if we're not already in a list
                        if not state['inside_numbered_list']:
                            close_open_tags()
                        formatted_response.append("<ul style='margin: 0; padding-left: 25px; line-height: 0.9; margin-bottom: 8px;'>")
                        state['inside_list'] = True
                    formatted_response.append(f"<li style='margin: 0; padding-left: 5px; line-height: 0.9; margin-bottom: 4px;'>{self._apply_text_formatting(content)}</li>")
                continue
            elif state['inside_list'] and not state['inside_numbered_list']:
                # Only close the bullet list if we're not inside a numbered list
                formatted_response.append("</ul>")
                state['inside_list'] = False

            # Handle numbered lists
            numbered_match = numbered_list_pattern.match(line)
            if numbered_match:
                number, content = numbered_match.groups()
                if content.strip().endswith(':'):
                    close_open_tags()
                    append_paragraph(f"{number}. {content}")
                    continue

                # Close any bullet list that might be open
                if state['inside_list']:
                    formatted_response.append("</ul>")
                    state['inside_list'] = False

                if not state['inside_numbered_list']:
                    # Starting a new numbered list
                    close_open_tags()
                    formatted_response.append("<ol style='margin: 0; padding-left: 25px; line-height: 0.9; margin-bottom: 8px;'>")
                    state['inside_numbered_list'] = True
                    state['current_list_number'] = 1
                else:
                    # Continuing an existing numbered list
                    state['current_list_number'] += 1

                # Use the value attribute to ensure correct numbering
                formatted_response.append(f"<li value='{state['current_list_number']}' style='margin: 0; padding-left: 5px; line-height: 0.9; margin-bottom: 4px;'>{self._apply_text_formatting(content)}</li>")
                state['last_list_item_was_numbered'] = True
                continue
            elif state['inside_numbered_list'] and not list_match:
                # Only close the numbered list if this isn't a bullet point
                # (which might be part of the current numbered item)
                if not line.strip():
                    # Empty line - check if the next line is a numbered item
                    continue_numbered_list = False
                    for future_line in response_lines[response_lines.index(line)+1:]:
                        if not future_line.strip():
                            continue
                        if numbered_list_pattern.match(future_line):
                            continue_numbered_list = True
                        break

                    if not continue_numbered_list:
                        close_open_tags()
                        state['last_list_item_was_numbered'] = False
                else:
                    # Non-empty, non-list line - end the numbered list
                    close_open_tags()
                    state['last_list_item_was_numbered'] = False

            # Handle tables
            is_table_row = stripped_line_logic.startswith('|') and stripped_line_logic.endswith('|') and '|' in stripped_line_logic[1:-1]
            table_keywords = ['table', 'sheet', 'grid', 'spreadsheet', 'tabular', 'rows and columns']
            user_requested_table = False
            if hasattr(self, 'latest_user_input'):
                user_input_lower = self.latest_user_input.lower()
                if any(keyword in user_input_lower for keyword in table_keywords):
                    user_requested_table = True

            if not line.startswith(("User:", "Human:", "Veritas:")) and (is_table_row or ('|' in line and user_requested_table)):
                if dash_line_pattern.match(stripped_line_logic):
                    continue # Skip separator lines

                if not state['inside_table']:
                    close_open_tags()
                    formatted_response.append("<table style='border-collapse: collapse; width: 40%; margin-bottom: 15px; border: 1px solid #444; background-color: #1a1a1a; font-size: 38%; margin: 0 auto;'>")
                    state['inside_table'] = True
                    if not hasattr(self, 'column_alignments'): self.column_alignments = []
                    if not hasattr(self, 'table_row_count'): self.table_row_count = 0
                    if not hasattr(self, 'max_table_columns'): self.max_table_columns = 0
                    self.column_alignments = []

                cells_raw = line.split('|')
                current_cells = cells_raw[1:-1] if (line.startswith('|') and line.endswith('|')) else cells_raw
                is_header = state['inside_table'] and self.table_row_count == 0
                if not self.column_alignments: self.column_alignments = ['left'] * len(current_cells)
                is_odd_row = self.table_row_count % 2 == 1
                self.table_row_count += 1

                if is_header: formatted_response.append("<tr>")
                elif is_odd_row: formatted_response.append("<tr style='background-color: #222222;'>")
                else: formatted_response.append("<tr>")

                self.max_table_columns = max(getattr(self, 'max_table_columns', 0), len(current_cells))
                max_cells_to_render = max(len(current_cells), self.max_table_columns)

                for idx in range(max_cells_to_render):
                    cell_content_raw = current_cells[idx].strip() if idx < len(current_cells) else ""
                    formatted_cell_content = self._apply_text_formatting(cell_content_raw)
                    alignment = self.column_alignments[idx] if idx < len(self.column_alignments) else 'left'
                    cell_display_text = formatted_cell_content if cell_content_raw else " "

                    if is_header:
                        formatted_response.append(f"<th style='border: 1px solid #555; padding: 4px; text-align: {alignment}; background-color: #222222; color: #ecf0f1; font-weight: bold;'>{cell_display_text}</th>")
                    else:
                        formatted_response.append(f"<td style='border: 1px solid #444; padding: 4px; text-align: {alignment};'>{cell_display_text}</td>")

                formatted_response.append("</tr>")
                continue
            elif state['inside_table']:
                close_open_tags()

            # Default: treat as standard paragraph
            close_open_tags()
            append_paragraph(line)

        # After processing all lines, close any remaining open tags
        close_open_tags()

        # Join the formatted lines into a single HTML string
        response_text = "".join(formatted_response)

        # --- Tag Balancing (remains the same) ---
        common_tags = ['p', 'div', 'span', 'strong', 'em', 'a', 'li', 'ul', 'ol', 'blockquote', 'b', 'font', 'table', 'tr', 'td', 'th']
        final_response_lines = list(formatted_response)

        for tag in common_tags:
            open_pattern = re.compile(f'<{tag}(?:\\s+[^>]*>|>)', re.IGNORECASE)
            open_count = len(open_pattern.findall(response_text))
            close_pattern = re.compile(f'</{tag}>', re.IGNORECASE)
            close_count = len(close_pattern.findall(response_text))

            if open_count > close_count:
                missing_closes = open_count - close_count
                # logger.debug(f"Found {open_count} <{tag}> tags and {close_count} </{tag}> tags. Adding {missing_closes} closing tags.")
                for _ in range(missing_closes):
                    final_response_lines.append(f"</{tag}>")
            elif close_count > open_count:
                 logger.warning(f"Found more closing tags ({close_count}) than opening tags ({open_count}) for <{tag}>. HTML might be invalid.")

        final_response = "".join(final_response_lines)

        return final_response

# EXTERNAL FEATURES ##################################################################################################################################################################################################

    def closeEvent(self, event):
        self.reset_chat()
        event.accept()

    def reset_chat(self, topic="New Session Topic"):
        """
        Smart reset chat functionality:
        - Only creates a new conversation if the current one has messages
        - Prevents duplicate empty conversations
        """
        try:
            # 1. Get the current active conversation
            active_conversation_uuid = get_active_conversation()

            # 2. Check if the current conversation has any messages
            has_messages = False
            if active_conversation_uuid:
                with get_db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM conversation_history WHERE conversation_uuid = ?", (active_conversation_uuid,))
                    message_count = cursor.fetchone()[0]
                    has_messages = message_count > 0
                    logger.info(f"Current conversation {active_conversation_uuid} has {message_count} messages")

            # 3. Backup current conversation if it has messages
            if has_messages:
                logger.info(f"Backing up current conversation before reset")
                self.backup_conversation_history()

                # Create a new conversation for the new session
                new_conversation_uuid = create_new_conversation("New Conversation")
                if new_conversation_uuid:
                    set_active_conversation(new_conversation_uuid)
                    logger.info(f"Created new conversation for reset: {new_conversation_uuid}")
                else:
                    logger.error("Failed to create new conversation during reset")
            else:
                # Current conversation is empty, just clear the display
                logger.info("Current conversation is empty, no need to create new conversation")
                if not active_conversation_uuid:
                    # No active conversation at all, create one
                    new_conversation_uuid = create_new_conversation("New Conversation")
                    if new_conversation_uuid:
                        set_active_conversation(new_conversation_uuid)
                        logger.info(f"No active conversation found, created: {new_conversation_uuid}")

            # 4. Clear the display
            self.outb.clear()

            # 5. Initialize variables for the new session
            self.current_session_log_title = "Veritas_User_Interaction_log" # Reset to default for the new session
            self.current_session_log_suffix = datetime.now().strftime("%Y%m%d%H%M%S%f") # Generate new unique ID for the new session
            self.db_title_set_for_current_session = False # Reset title set flag for the new session

            self.update_placeholder_text()
            logger.info(f"Chat reset completed. Active conversation: {get_active_conversation()}")

        except Exception as e:
            logger.error(f"Error during chat reset: {e}", exc_info=True)

    def load_history_from_db(self, db_path, instant=False):
        self.db_title_set = True
        try:
            # Ensure the main database has the proper schema
            migrate_database_schema()

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='conversation_history';")
                table_exists = cursor.fetchone()
                if not table_exists:
                    self.communicator.response_received.emit(f"Error: Could not load history from {db_path}. Table 'conversation_history' is missing.")
                    return

                cursor.execute("SELECT role, message FROM conversation_history ORDER BY id ASC")
                messages = cursor.fetchall()

                if messages:
                    # Create a new conversation for the loaded history
                    conversation_title = os.path.basename(db_path).replace('.db', '').replace('_', ' ')
                    loaded_conversation_uuid = create_new_conversation(conversation_title)

                    if loaded_conversation_uuid:
                        # Set this as the active conversation
                        set_active_conversation(loaded_conversation_uuid)

                        # Clear current conversation history and load the selected history
                        with sqlite3.connect(self.combined_db_file_path) as main_conn:
                            main_cursor = main_conn.cursor()
                            # Delete only messages from the active conversation
                            main_cursor.execute("DELETE FROM conversation_history WHERE conversation_uuid = ?", (loaded_conversation_uuid,))

                            for role, message in messages:
                                # Insert with the conversation UUID
                                main_cursor.execute(
                                    "INSERT INTO conversation_history (conversation_uuid, role, message) VALUES (?, ?, ?)",
                                    (loaded_conversation_uuid, role, message)
                                )
                                if role == 'User':
                                    formatted_message = f"<b><font color='#00FFFF'>User:</font></b><br>{message}"
                                    self.communicator.response_received.emit(formatted_message)

                                elif role == 'Veritas':
                                    formatted_message = f"<b><br><font color='#00FFFF'>Veritas:</font></b><br>{message}<br>"
                                    self.communicator.response_received.emit(formatted_message)

                                elif role == 'alert':
                                    formatted_message = f"<b><font color='green'> >>> {message}</font></b>"
                                    self.communicator.response_received.emit(formatted_message)

                            main_conn.commit()
                            logger.info(f"Loaded history into conversation: {loaded_conversation_uuid}")
                    else:
                        self.communicator.response_received.emit(f"Error: Could not create conversation for loaded history.")
                else:
                    self.communicator.response_received.emit(f"No messages found in {db_path}.")

        except sqlite3.Error as e:
            self.communicator.response_received.emit(f"Error: Could not load history from {db_path}. {str(e)}")
            logger.error(f"SQLite error loading history from {db_path}: {e}", exc_info=True)

    def load_history_files(self):
        """
        Updated to load conversations from the database instead of scanning files.
        Also includes migration functionality for existing backup files.
        """
        self.history_list.clear()

        try:
            # First, check for and migrate any existing backup files
            self.migrate_backup_files_to_database()

            # Load conversations from the database
            conversations = get_all_conversations()

            for conversation_uuid, title, created_at, updated_at, is_active in conversations:
                # Use just the title without date or active status
                display_name = title

                item = QListWidgetItem(display_name)
                item.setData(Qt.UserRole, conversation_uuid)  # Store the conversation UUID
                item.setData(Qt.UserRole + 1, "conversation")  # Mark as conversation (not file)
                self.history_list.addItem(item)

            logger.info(f"Loaded {len(conversations)} conversations from database")

        except Exception as e:
            logger.error(f"Error loading conversations: {str(e)}", exc_info=True)

    def load_selected_history(self, item):
        """
        Updated to handle both conversation UUIDs and legacy file paths.
        """
        item_data = item.data(Qt.UserRole)
        item_type = item.data(Qt.UserRole + 1)

        if not item_data:
            logger.error(f"No data found for selected item: {item.text()}")
            return

        self.outb.clear()

        if item_type == "conversation":
            # Loading a conversation from the database
            conversation_uuid = item_data
            try:
                # Set this conversation as active
                set_active_conversation(conversation_uuid)

                # Load the conversation history
                conversation_history = load_conversation_history(conversation_uuid=conversation_uuid, full_history=True)

                # Display the conversation history
                for role, message, timestamp_str in conversation_history:
                    if role == 'User':
                        formatted_message = f"<b><font color='#00FFFF'>User:</font></b><br>{message}"
                        self.communicator.response_received.emit(formatted_message)
                    elif role == 'Veritas':
                        formatted_message = f"<b><br><font color='#00FFFF'>Veritas:</font></b><br>{message}<br>"
                        self.communicator.response_received.emit(formatted_message)
                    elif role == 'alert':
                        formatted_message = f"<b><font color='green'> >>> {message}</font></b>"
                        self.communicator.response_received.emit(formatted_message)

                # Update session trackers
                with get_db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT title FROM conversations WHERE conversation_uuid = ?", (conversation_uuid,))
                    result = cursor.fetchone()
                    if result:
                        self.current_session_log_title = result[0]
                        self.db_title_set_for_current_session = True

                logger.info(f"Loaded conversation: {conversation_uuid}")

            except Exception as e:
                logger.error(f"Error loading conversation {conversation_uuid}: {e}", exc_info=True)
                self.communicator.response_received.emit(f"Error: Could not load conversation.")

        else:
            # Legacy file loading (for backward compatibility)
            full_filename = item_data
            db_path = os.path.join(history_log_dir, full_filename)

            if not os.path.exists(db_path):
                logger.error(f"History file does not exist: {db_path}")
                self.communicator.response_received.emit(f"Error: Selected history file not found.")
                return

            # Update current session trackers to match the loaded history
            match = re.match(r'^(.*?)_(\d{14}\d*)\.db$', full_filename)
            if match:
                self.current_session_log_title = match.group(1).replace("_", " ")
                self.current_session_log_suffix = match.group(2)
                self.db_title_set_for_current_session = True
                logger.info(f"Loading legacy session: Title='{self.current_session_log_title}', Suffix='{self.current_session_log_suffix}'")
            else:
                self.current_session_log_title = full_filename.replace(".db","")
                self.current_session_log_suffix = datetime.now().strftime("%Y%m%d%H%M%S%f")
                self.db_title_set_for_current_session = True
                logger.warning(f"Could not parse session suffix from '{full_filename}'. Using new suffix if session is modified.")

            self.load_history_from_db(db_path, instant=True)

        self.update_placeholder_text() # Update placeholder after loading

    def migrate_backup_files_to_database(self):
        """
        Migrate existing backup .db files into the consolidated database system.
        This runs automatically when loading history files.
        """
        backup_dir = os.path.join(script_dir, "Previous History Log")
        if not os.path.exists(backup_dir):
            return

        try:
            migrated_count = 0
            for filename in os.listdir(backup_dir):
                if filename.endswith('.db'):
                    db_path = os.path.join(backup_dir, filename)

                    # Check if this file has already been migrated
                    # We'll use the filename as a unique identifier
                    with get_db_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            "SELECT COUNT(*) FROM conversations WHERE title LIKE ?",
                            (f"%{filename.replace('.db', '').replace('_', ' ')}%",)
                        )
                        if cursor.fetchone()[0] > 0:
                            continue  # Already migrated

                    # Extract title from filename
                    match = re.match(r'^(.*?)_(\d{14}\d*)\.db$', filename)
                    if match:
                        title = match.group(1).replace("_", " ")
                        timestamp_suffix = match.group(2)
                        try:
                            # Try to parse the timestamp for created_at
                            created_at = datetime.strptime(timestamp_suffix[:14], "%Y%m%d%H%M%S")
                        except ValueError:
                            created_at = datetime.now()
                    else:
                        title = filename.replace(".db", "").replace("_", " ")
                        created_at = datetime.now()

                    # Load messages from the backup file
                    try:
                        with sqlite3.connect(db_path) as backup_conn:
                            backup_cursor = backup_conn.cursor()
                            backup_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='conversation_history'")
                            if not backup_cursor.fetchone():
                                continue  # No conversation_history table

                            backup_cursor.execute("SELECT role, message, timestamp FROM conversation_history ORDER BY id ASC")
                            messages = backup_cursor.fetchall()

                            if not messages:
                                continue  # No messages to migrate

                        # Create new conversation in the database
                        conversation_uuid = str(uuid.uuid4())
                        with get_db_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute(
                                "INSERT INTO conversations (conversation_uuid, title, created_at, updated_at, is_active) VALUES (?, ?, ?, ?, ?)",
                                (conversation_uuid, title, created_at, created_at, 0)  # Not active by default
                            )

                            # Insert all messages
                            for role, message, timestamp in messages:
                                cursor.execute(
                                    "INSERT INTO conversation_history (conversation_uuid, role, message, timestamp) VALUES (?, ?, ?, ?)",
                                    (conversation_uuid, role, message, timestamp)
                                )

                            conn.commit()
                            migrated_count += 1
                            logger.info(f"Migrated backup file '{filename}' to conversation '{title}' ({conversation_uuid})")

                    except Exception as e:
                        logger.error(f"Error migrating backup file '{filename}': {e}")
                        continue

            if migrated_count > 0:
                logger.info(f"Successfully migrated {migrated_count} backup files to database")

        except Exception as e:
            logger.error(f"Error during backup file migration: {e}", exc_info=True)

    def cleanup_migrated_backup_files(self):
        """
        Optional function to remove backup .db files after successful migration.
        This should only be called after confirming migration was successful.
        """
        backup_dir = os.path.join(script_dir, "Previous History Log")
        if not os.path.exists(backup_dir):
            return

        try:
            removed_count = 0
            for filename in os.listdir(backup_dir):
                if filename.endswith('.db'):
                    # Check if this conversation exists in the database
                    title = filename.replace('.db', '').replace('_', ' ')
                    with get_db_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            "SELECT COUNT(*) FROM conversations WHERE title LIKE ?",
                            (f"%{title}%",)
                        )
                        if cursor.fetchone()[0] > 0:
                            # Conversation exists in database, safe to remove file
                            db_path = os.path.join(backup_dir, filename)
                            os.remove(db_path)
                            removed_count += 1
                            logger.info(f"Removed migrated backup file: {filename}")

            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} migrated backup files")

        except Exception as e:
            logger.error(f"Error during backup file cleanup: {e}", exc_info=True)

    def backup_conversation_history(self):
        """
        Updated backup system: Instead of creating separate .db files,
        this now ensures the current conversation is properly saved in the conversations table.
        The actual backup is handled by the UUID-based conversation system.
        """
        try:
            # Get the active conversation
            active_conversation_uuid = get_active_conversation()

            if not active_conversation_uuid:
                logger.warning("No active conversation found for backup.")
                return

            # Update the conversation title if one was set during this session
            if self.db_title_set_for_current_session and self.current_session_log_title != "Veritas_User_Interaction_log":
                clean_title = re.sub(r'[^\w\s-]', '', self.current_session_log_title.replace("_", " ")).strip()
                if clean_title:
                    update_conversation_title(active_conversation_uuid, clean_title)
                    logger.info(f"Updated conversation title to: {clean_title}")

            # Check if the conversation has any messages
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM conversation_history WHERE conversation_uuid = ?", (active_conversation_uuid,))
                message_count = cursor.fetchone()[0]

                if message_count == 0:
                    logger.info("No messages in current conversation, backup skipped.")
                    return

                # Update the conversation's updated_at timestamp
                cursor.execute(
                    "UPDATE conversations SET updated_at = ? WHERE conversation_uuid = ?",
                    (datetime.now(), active_conversation_uuid)
                )
                conn.commit()

                logger.info(f"Conversation {active_conversation_uuid} backed up successfully with {message_count} messages.")

        except Exception as e:
            logger.error(f"Error during conversation backup: {e}", exc_info=True)

    def clear_memory(self):
        try:
            with get_db_connection() as conn:
                conn.execute("DELETE FROM mind")

            logger.info("Memory cleared successfully.")
            self.load_history_files()

        except sqlite3.Error as e:
            logger.error(f"Database error while clearing memory: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")

    def _clear_database(self):
        """
        Nuclear option: Completely clears ALL conversation data and creates a fresh start.
        This is different from reset_chat which is smart about conversation management.
        """
        if not os.path.exists(self.combined_db_file_path):
            print(f"Database file does not exist at: {self.combined_db_file_path}")
            return

        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                # Nuclear option: Delete ALL conversation data
                cursor.execute("DELETE FROM conversation_history")
                cursor.execute("DELETE FROM conversations")
                conn.commit()

            print(f"All conversation history cleared successfully at: {self.combined_db_file_path}")

            # Create a fresh new conversation after clearing everything
            new_conversation_uuid = create_new_conversation("New Conversation")
            if new_conversation_uuid:
                set_active_conversation(new_conversation_uuid)
                print(f"Created fresh conversation: {new_conversation_uuid}")
                logger.info(f"Database completely cleared. Created fresh conversation: {new_conversation_uuid}")
            else:
                print("Warning: Failed to create new conversation after clearing database")
                logger.error("Failed to create new conversation after clearing database")

        except sqlite3.Error as e:
            print(f"An error occurred while clearing the database: {e}\n {traceback.format_exc()}")
            logger.error(f"Database clear error: {e}", exc_info=True)

# END ##################################################################################################################################################################################################
if __name__ == "__main__":
    create_tables()
    SECURITY()
    conversation_history = load_conversation_history()

    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    app = QApplication(sys.argv)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
    app.setStyle("Fusion")

    window = MainWindow()
    window.show()

    # Check if terms have been accepted
    QTimer.singleShot(500, window.check_terms_acceptance)

    sys.exit(app.exec_())


#Copyright (c) 2024 Bradley Bulman. All rights reserved.

#Copyright (c) 2024 Bradley Bulman. All rights reserved.

#Project: Project Veritas -
#Date: 2024-09-02 12:05:52
#Author: Bradley Bulman

#This software is provided under the MIT License.
###########################################################################################################################################################################################################################################